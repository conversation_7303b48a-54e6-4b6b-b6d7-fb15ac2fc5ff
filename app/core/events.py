"""
Event-driven communication system for the supplement tracking platform.

This module provides a simple event system for decoupled communication
between different modules, following the observer pattern.
"""

from typing import Any, Callable, Dict, List
from enum import Enum
import asyncio
import logging

logger = logging.getLogger(__name__)


class EventType(str, Enum):
    """
    Enumeration of all event types in the system.
    
    This enum defines all possible events that can be triggered
    throughout the application for type safety and documentation.
    """
    
    # User events
    USER_REGISTERED = "user.registered"
    USER_LOGIN = "user.login"
    USER_LOGOUT = "user.logout"
    USER_PROFILE_UPDATED = "user.profile_updated"
    
    # Supplement tracking events
    SUPPLEMENT_ADDED = "supplement.added"
    SUPPLEMENT_LOGGED = "supplement.logged"
    SUPPLEMENT_STACK_CREATED = "supplement.stack_created"
    
    # Community events
    POST_CREATED = "community.post_created"
    COMMENT_ADDED = "community.comment_added"
    PEER_REVIEW_SUBMITTED = "community.peer_review_submitted"
    
    # Research events
    EXPERIMENT_STARTED = "research.experiment_started"
    EXPERIMENT_COMPLETED = "research.experiment_completed"
    CORRELATION_DISCOVERED = "research.correlation_discovered"
    
    # System events
    SYSTEM_HEALTH_CHECK = "system.health_check"
    BACKGROUND_TASK_COMPLETED = "system.background_task_completed"


class EventBus:
    """
    Simple event bus for application-wide event handling.
    
    This class implements the observer pattern to allow different parts
    of the application to communicate without tight coupling.
    
    Attributes:
        _handlers: Dictionary mapping event types to their handler functions
    """
    
    def __init__(self) -> None:
        """Initialize the event bus with empty handlers."""
        self._handlers: Dict[EventType, List[Callable]] = {}
    
    def subscribe(self, event_type: EventType, handler: Callable) -> None:
        """
        Subscribe a handler function to an event type.
        
        Args:
            event_type: The type of event to listen for
            handler: The function to call when the event is triggered
            
        Example:
            >>> bus = EventBus()
            >>> def handle_user_login(event_data):
            ...     print(f"User logged in: {event_data['user_id']}")
            >>> bus.subscribe(EventType.USER_LOGIN, handle_user_login)
        """
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)
        logger.debug(f"Subscribed handler for event: {event_type}")
    
    def unsubscribe(self, event_type: EventType, handler: Callable) -> None:
        """
        Unsubscribe a handler function from an event type.
        
        Args:
            event_type: The type of event to stop listening for
            handler: The handler function to remove
        """
        if event_type in self._handlers:
            try:
                self._handlers[event_type].remove(handler)
                logger.debug(f"Unsubscribed handler for event: {event_type}")
            except ValueError:
                logger.warning(f"Handler not found for event: {event_type}")
    
    async def publish(self, event_type: EventType, event_data: Dict[str, Any]) -> None:
        """
        Publish an event to all subscribed handlers.
        
        Args:
            event_type: The type of event being published
            event_data: Data associated with the event
            
        Example:
            >>> bus = EventBus()
            >>> await bus.publish(EventType.USER_LOGIN, {"user_id": "123"})
        """
        if event_type not in self._handlers:
            logger.debug(f"No handlers registered for event: {event_type}")
            return
        
        handlers = self._handlers[event_type]
        logger.info(f"Publishing event {event_type} to {len(handlers)} handlers")
        
        # Execute all handlers concurrently
        tasks = []
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    tasks.append(handler(event_data))
                else:
                    # Run sync handlers in thread pool
                    tasks.append(asyncio.get_event_loop().run_in_executor(
                        None, handler, event_data
                    ))
            except Exception as e:
                logger.error(f"Error preparing handler for {event_type}: {e}")
        
        # Wait for all handlers to complete
        if tasks:
            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except Exception as e:
                logger.error(f"Error executing handlers for {event_type}: {e}")
    
    def get_handler_count(self, event_type: EventType) -> int:
        """
        Get the number of handlers registered for an event type.
        
        Args:
            event_type: The event type to check
            
        Returns:
            int: Number of registered handlers
        """
        return len(self._handlers.get(event_type, []))


# Global event bus instance
event_bus = EventBus()


def subscribe_to_event(event_type: EventType):
    """
    Decorator for subscribing functions to events.
    
    Args:
        event_type: The event type to subscribe to
        
    Returns:
        Decorator function
        
    Example:
        >>> @subscribe_to_event(EventType.USER_LOGIN)
        ... async def handle_user_login(event_data):
        ...     print(f"User {event_data['user_id']} logged in")
    """
    def decorator(func: Callable) -> Callable:
        event_bus.subscribe(event_type, func)
        return func
    return decorator
