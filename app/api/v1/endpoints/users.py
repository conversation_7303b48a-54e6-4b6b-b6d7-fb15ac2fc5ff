"""
User management endpoints for the supplement tracking platform.

This module provides endpoints for user registration, profile management,
and user-related operations.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_db
from app.modules.user_management.dependencies import get_current_user, get_current_superuser
from app.modules.user_management.models import User
from app.modules.user_management.schemas import (
    UserCreate, UserResponse, UserUpdate, UserLoginResponse
)
from app.modules.user_management.services import UserService

router = APIRouter()


@router.post("/register", response_model=UserLoginResponse)
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    User registration endpoint.

    Creates a new user account in the system and returns an access token.

    Args:
        user_data: User registration data
        db: Database session

    Returns:
        UserLoginResponse: Registration confirmation with access token and user details

    Raises:
        HTTPException: If registration fails (email/username taken, etc.)
    """
    # Check if registration is allowed
    if not settings.USERS_OPEN_REGISTRATION:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Open registration is not allowed"
        )

    user_service = UserService(db)

    try:
        # Create new user
        user = await user_service.create_user(user_data)

        # Create access token for immediate login
        access_token = await user_service.create_access_token_for_user(user)

        return UserLoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user profile endpoint.

    Returns the authenticated user's profile information.

    Args:
        current_user: Current authenticated user

    Returns:
        UserResponse: User profile data
    """
    return current_user


@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update user profile endpoint.

    Updates the authenticated user's profile information.

    Args:
        user_data: Updated user data
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserResponse: Updated user profile data

    Raises:
        HTTPException: If update fails (email/username conflicts, etc.)
    """
    user_service = UserService(db)

    try:
        updated_user = await user_service.update_user(current_user.id, user_data)

        if updated_user is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        return updated_user

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user information (alias for /profile).

    Args:
        current_user: Current authenticated user

    Returns:
        UserResponse: Current user information
    """
    return current_user


@router.post("/deactivate")
async def deactivate_account(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Deactivate current user account.

    Args:
        current_user: Current authenticated user
        db: Database session

    Returns:
        dict: Deactivation confirmation
    """
    user_service = UserService(db)

    success = await user_service.deactivate_user(current_user.id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return {"message": "Account deactivated successfully"}


# Admin endpoints
@router.post("/admin/activate/{user_id}")
async def activate_user_admin(
    user_id: str,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db)
):
    """
    Activate a user account (admin only).

    Args:
        user_id: User ID to activate
        current_user: Current superuser
        db: Database session

    Returns:
        dict: Activation confirmation

    Raises:
        HTTPException: If user not found or invalid UUID
    """
    user_service = UserService(db)

    try:
        from uuid import UUID
        user_uuid = UUID(user_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID format"
        )

    success = await user_service.activate_user(user_uuid)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return {"message": f"User {user_id} activated successfully"}
