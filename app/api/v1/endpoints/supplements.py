"""
Supplement tracking endpoints for the supplement tracking platform.

This module provides endpoints for supplement management, tracking,
and related operations.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.modules.user_management.dependencies import get_current_user, get_optional_current_user
from app.modules.user_management.models import User
from app.modules.supplement_tracking.schemas import (
    SupplementCreate, SupplementResponse, SupplementUpdate,
    SupplementIntakeCreate, SupplementIntakeResponse,
    SupplementListResponse
)
from app.modules.supplement_tracking.services import SupplementService

router = APIRouter()


@router.get("/", response_model=SupplementListResponse)
async def list_supplements(
    search: Optional[str] = Query(None, description="Search term for name, brand, or description"),
    category: Optional[str] = Query(None, description="Filter by supplement category"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Maximum number of records to return"),
    db: AsyncSession = Depends(get_db)
):
    """
    List supplements endpoint.

    Returns a paginated list of available supplements with optional filtering.

    Args:
        search: Search term for name, brand, or description
        category: Filter by supplement category
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        db: Database session

    Returns:
        SupplementListResponse: List of supplements with pagination info
    """
    supplement_service = SupplementService(db)

    supplements = await supplement_service.get_supplements(
        skip=skip,
        limit=limit,
        category=category,
        search=search
    )

    # For now, we'll return the count as the length of results
    # In a real implementation, you'd want a separate count query
    total = len(supplements)

    return SupplementListResponse(
        supplements=supplements,
        total=total,
        skip=skip,
        limit=limit
    )


@router.post("/", response_model=SupplementResponse)
async def create_supplement(
    supplement_data: SupplementCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Create supplement endpoint.

    Adds a new supplement to the database.

    Args:
        supplement_data: Supplement creation data
        current_user: Current authenticated user
        db: Database session

    Returns:
        SupplementResponse: Created supplement details

    Raises:
        HTTPException: If creation fails (barcode conflict, etc.)
    """
    supplement_service = SupplementService(db)

    try:
        supplement = await supplement_service.create_supplement(
            supplement_data,
            created_by_user_id=current_user.id
        )
        return supplement

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{supplement_id}", response_model=SupplementResponse)
async def get_supplement(
    supplement_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get supplement by ID endpoint.

    Returns detailed information about a specific supplement.

    Args:
        supplement_id: Supplement's unique identifier
        db: Database session

    Returns:
        SupplementResponse: Supplement details

    Raises:
        HTTPException: If supplement not found
    """
    supplement_service = SupplementService(db)

    supplement = await supplement_service.get_supplement_by_id(supplement_id)

    if not supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )

    return supplement


@router.put("/{supplement_id}", response_model=SupplementResponse)
async def update_supplement(
    supplement_id: UUID,
    supplement_data: SupplementUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update supplement endpoint.

    Updates supplement information. Only the user who created the supplement
    or a superuser can update it.

    Args:
        supplement_id: Supplement's unique identifier
        supplement_data: Updated supplement data
        current_user: Current authenticated user
        db: Database session

    Returns:
        SupplementResponse: Updated supplement details

    Raises:
        HTTPException: If supplement not found or user lacks permission
    """
    supplement_service = SupplementService(db)

    # Check if supplement exists and user has permission
    existing_supplement = await supplement_service.get_supplement_by_id(supplement_id)
    if not existing_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )

    # Check permissions (creator or superuser)
    if (existing_supplement.created_by_user_id != current_user.id and
        not current_user.is_superuser):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to update this supplement"
        )

    try:
        updated_supplement = await supplement_service.update_supplement(
            supplement_id,
            supplement_data
        )
        return updated_supplement

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{supplement_id}")
async def delete_supplement(
    supplement_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete supplement endpoint.

    Deletes a supplement. Only the user who created the supplement
    or a superuser can delete it.

    Args:
        supplement_id: Supplement's unique identifier
        current_user: Current authenticated user
        db: Database session

    Returns:
        dict: Deletion confirmation

    Raises:
        HTTPException: If supplement not found or user lacks permission
    """
    supplement_service = SupplementService(db)

    # Check if supplement exists and user has permission
    existing_supplement = await supplement_service.get_supplement_by_id(supplement_id)
    if not existing_supplement:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )

    # Check permissions (creator or superuser)
    if (existing_supplement.created_by_user_id != current_user.id and
        not current_user.is_superuser):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to delete this supplement"
        )

    success = await supplement_service.delete_supplement(supplement_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supplement not found"
        )

    return {"message": "Supplement deleted successfully"}


@router.post("/{supplement_id}/track", response_model=SupplementIntakeResponse)
async def track_supplement_intake(
    supplement_id: UUID,
    intake_data: SupplementIntakeCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Track supplement intake endpoint.

    Records supplement intake for the authenticated user.

    Args:
        supplement_id: Supplement's unique identifier
        intake_data: Intake logging data
        current_user: Current authenticated user
        db: Database session

    Returns:
        SupplementIntakeResponse: Tracking confirmation and details

    Raises:
        HTTPException: If supplement not found
    """
    supplement_service = SupplementService(db)

    try:
        intake = await supplement_service.log_supplement_intake(
            user_id=current_user.id,
            supplement_id=supplement_id,
            intake_data=intake_data
        )
        return intake

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/intake/history", response_model=List[SupplementIntakeResponse])
async def get_supplement_history(
    supplement_id: Optional[UUID] = Query(None, description="Filter by specific supplement"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of records to return"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get supplement history endpoint.

    Returns the user's supplement intake history.

    Args:
        supplement_id: Filter by specific supplement (optional)
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        current_user: Current authenticated user
        db: Database session

    Returns:
        List[SupplementIntakeResponse]: Supplement intake history with pagination
    """
    supplement_service = SupplementService(db)

    intake_history = await supplement_service.get_user_intake_history(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        supplement_id=supplement_id
    )

    return intake_history
