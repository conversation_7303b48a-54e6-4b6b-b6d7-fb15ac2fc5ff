"""
User management dependencies for FastAPI.

This module provides dependency injection functions for user authentication
and authorization in FastAPI endpoints.
"""

from typing import Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import decode_access_token
from app.modules.user_management.models import User
from app.modules.user_management.services import UserService

# HTTP Bearer token scheme for authentication
security = HTTPBearer()


async def get_user_service(db: AsyncSession = Depends(get_db)) -> UserService:
    """
    Get user service instance.
    
    Args:
        db: Database session
        
    Returns:
        UserService: User service instance
    """
    return UserService(db)


async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> UUID:
    """
    Extract and validate user ID from JW<PERSON> token.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        UUID: Current user's ID
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    token = credentials.credentials
    payload = decode_access_token(token)
    
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_id_str = payload.get("sub")
    if user_id_str is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        user_id = UUID(user_id_str)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user_id


async def get_current_user(
    user_id: UUID = Depends(get_current_user_id),
    user_service: UserService = Depends(get_user_service)
) -> User:
    """
    Get the current authenticated user.
    
    Args:
        user_id: Current user's ID from token
        user_service: User service instance
        
    Returns:
        User: Current user instance
        
    Raises:
        HTTPException: If user not found or inactive
    """
    user = await user_service.get_user_by_id(user_id)
    
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current active user (alias for get_current_user).
    
    Args:
        current_user: Current user from authentication
        
    Returns:
        User: Current active user
    """
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current user and verify they are a superuser.
    
    Args:
        current_user: Current user from authentication
        
    Returns:
        User: Current superuser
        
    Raises:
        HTTPException: If user is not a superuser
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return current_user


async def get_optional_current_user(
    user_service: UserService = Depends(get_user_service),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(
        HTTPBearer(auto_error=False)
    )
) -> Optional[User]:
    """
    Get the current user if authenticated, None otherwise.
    
    This dependency is useful for endpoints that work for both
    authenticated and anonymous users.
    
    Args:
        user_service: User service instance
        credentials: Optional HTTP authorization credentials
        
    Returns:
        Optional[User]: Current user if authenticated, None otherwise
    """
    if credentials is None:
        return None
    
    try:
        token = credentials.credentials
        payload = decode_access_token(token)
        
        if payload is None:
            return None
        
        user_id_str = payload.get("sub")
        if user_id_str is None:
            return None
        
        user_id = UUID(user_id_str)
        user = await user_service.get_user_by_id(user_id)
        
        if user is None or not user.is_active:
            return None
        
        return user
    
    except (ValueError, Exception):
        return None
