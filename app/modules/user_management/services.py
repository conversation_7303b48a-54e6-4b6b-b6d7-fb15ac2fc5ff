"""
User management service layer.

This module provides business logic for user operations including
authentication, registration, and profile management.
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import create_access_token, get_password_hash, verify_password
from app.modules.user_management.models import User, UserSession
from app.modules.user_management.schemas import UserCreate, UserLogin, UserUpdate


class UserService:
    """
    Service class for user management operations.
    
    This class encapsulates all business logic related to user operations,
    providing a clean interface for controllers and maintaining separation
    of concerns.
    """
    
    def __init__(self, db: AsyncSession):
        """
        Initialize the user service.
        
        Args:
            db: Database session for operations
        """
        self.db = db
    
    async def create_user(self, user_data: UserCreate) -> User:
        """
        Create a new user account.
        
        Args:
            user_data: User creation data
            
        Returns:
            User: Created user instance
            
        Raises:
            ValueError: If email or username already exists
        """
        # Check if email already exists
        existing_email = await self.db.execute(
            select(User).where(User.email == user_data.email)
        )
        if existing_email.scalar_one_or_none():
            raise ValueError("Email already registered")
        
        # Check if username already exists
        existing_username = await self.db.execute(
            select(User).where(User.username == user_data.username)
        )
        if existing_username.scalar_one_or_none():
            raise ValueError("Username already taken")
        
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        user = User(
            email=user_data.email,
            username=user_data.username,
            hashed_password=hashed_password,
            full_name=user_data.full_name,
            bio=user_data.bio,
        )
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
    
    async def authenticate_user(self, login_data: UserLogin) -> Optional[User]:
        """
        Authenticate a user with email and password.
        
        Args:
            login_data: User login credentials
            
        Returns:
            Optional[User]: User instance if authentication successful, None otherwise
        """
        # Find user by email
        result = await self.db.execute(
            select(User).where(User.email == login_data.email)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return None
        
        if not user.is_active:
            return None
        
        if not verify_password(login_data.password, user.hashed_password):
            return None
        
        # Update last login time
        user.last_login_at = datetime.utcnow()
        await self.db.commit()
        
        return user
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """
        Get a user by their ID.
        
        Args:
            user_id: User's unique identifier
            
        Returns:
            Optional[User]: User instance if found, None otherwise
        """
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Get a user by their email address.
        
        Args:
            email: User's email address
            
        Returns:
            Optional[User]: User instance if found, None otherwise
        """
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def update_user(self, user_id: UUID, user_data: UserUpdate) -> Optional[User]:
        """
        Update a user's profile information.
        
        Args:
            user_id: User's unique identifier
            user_data: Updated user data
            
        Returns:
            Optional[User]: Updated user instance if found, None otherwise
            
        Raises:
            ValueError: If email or username already exists for another user
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            return None
        
        # Check for email conflicts if email is being updated
        if user_data.email and user_data.email != user.email:
            existing_email = await self.db.execute(
                select(User).where(
                    User.email == user_data.email,
                    User.id != user_id
                )
            )
            if existing_email.scalar_one_or_none():
                raise ValueError("Email already registered")
            user.email = user_data.email
        
        # Check for username conflicts if username is being updated
        if user_data.username and user_data.username != user.username:
            existing_username = await self.db.execute(
                select(User).where(
                    User.username == user_data.username,
                    User.id != user_id
                )
            )
            if existing_username.scalar_one_or_none():
                raise ValueError("Username already taken")
            user.username = user_data.username
        
        # Update other fields
        if user_data.full_name is not None:
            user.full_name = user_data.full_name
        if user_data.bio is not None:
            user.bio = user_data.bio
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
    
    async def create_access_token_for_user(self, user: User) -> str:
        """
        Create an access token for a user.
        
        Args:
            user: User instance
            
        Returns:
            str: JWT access token
        """
        return create_access_token(subject=str(user.id))
    
    async def deactivate_user(self, user_id: UUID) -> bool:
        """
        Deactivate a user account.
        
        Args:
            user_id: User's unique identifier
            
        Returns:
            bool: True if user was deactivated, False if not found
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.is_active = False
        await self.db.commit()
        
        return True
    
    async def activate_user(self, user_id: UUID) -> bool:
        """
        Activate a user account.
        
        Args:
            user_id: User's unique identifier
            
        Returns:
            bool: True if user was activated, False if not found
        """
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.is_active = True
        await self.db.commit()
        
        return True
