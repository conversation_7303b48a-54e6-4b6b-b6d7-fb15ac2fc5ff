"""
User management Pydantic schemas for request/response validation.

This module defines Pydantic models for API request and response validation,
following PEP 484 type hints and providing comprehensive data validation.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, validator


class UserBase(BaseModel):
    """
    Base user schema with common fields.
    
    This schema contains fields that are common across different
    user-related operations.
    
    Attributes:
        email: User's email address
        username: User's chosen username
        full_name: User's full name (optional)
        bio: User biography (optional)
    """
    
    email: EmailStr = Field(..., description="User's email address")
    username: str = Field(
        ...,
        min_length=3,
        max_length=50,
        regex="^[a-zA-Z0-9_-]+$",
        description="User's chosen username (alphanumeric, underscore, hyphen only)"
    )
    full_name: Optional[str] = Field(
        None,
        max_length=255,
        description="User's full name"
    )
    bio: Optional[str] = Field(
        None,
        max_length=1000,
        description="User biography or description"
    )


class UserCreate(UserBase):
    """
    Schema for user creation requests.
    
    This schema is used when creating new user accounts and includes
    the password field for registration.
    
    Attributes:
        password: Plain text password for the new account
    """
    
    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="Password (minimum 8 characters)"
    )
    
    @validator("password")
    def validate_password_strength(cls, v: str) -> str:
        """
        Validate password strength requirements.
        
        Args:
            v: Password string to validate
            
        Returns:
            str: Validated password
            
        Raises:
            ValueError: If password doesn't meet requirements
        """
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, and one digit"
            )
        
        return v


class UserUpdate(BaseModel):
    """
    Schema for user profile update requests.
    
    This schema allows partial updates to user profile information.
    All fields are optional to support partial updates.
    
    Attributes:
        email: Updated email address (optional)
        username: Updated username (optional)
        full_name: Updated full name (optional)
        bio: Updated biography (optional)
    """
    
    email: Optional[EmailStr] = Field(None, description="Updated email address")
    username: Optional[str] = Field(
        None,
        min_length=3,
        max_length=50,
        regex="^[a-zA-Z0-9_-]+$",
        description="Updated username"
    )
    full_name: Optional[str] = Field(
        None,
        max_length=255,
        description="Updated full name"
    )
    bio: Optional[str] = Field(
        None,
        max_length=1000,
        description="Updated biography"
    )


class UserResponse(UserBase):
    """
    Schema for user data in API responses.
    
    This schema represents user data returned by the API, excluding
    sensitive information like passwords.
    
    Attributes:
        id: User's unique identifier
        is_active: Whether the user account is active
        is_verified: Whether the user's email is verified
        created_at: Account creation timestamp
        updated_at: Last profile update timestamp
        last_login_at: Last login timestamp (optional)
    """
    
    id: UUID = Field(..., description="User's unique identifier")
    is_active: bool = Field(..., description="Whether the user account is active")
    is_verified: bool = Field(..., description="Whether the user's email is verified")
    created_at: datetime = Field(..., description="Account creation timestamp")
    updated_at: datetime = Field(..., description="Last profile update timestamp")
    last_login_at: Optional[datetime] = Field(
        None,
        description="Last login timestamp"
    )
    
    class Config:
        """Pydantic configuration for UserResponse."""
        
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
        }


class UserLogin(BaseModel):
    """
    Schema for user login requests.
    
    This schema validates login credentials provided by users.
    
    Attributes:
        email: User's email address
        password: User's password
    """
    
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., description="User's password")


class UserLoginResponse(BaseModel):
    """
    Schema for user login responses.
    
    This schema represents the response returned after successful login.
    
    Attributes:
        access_token: JWT access token
        token_type: Type of token (always "bearer")
        expires_in: Token expiration time in seconds
        user: User information
    """
    
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserResponse = Field(..., description="User information")


class PasswordChange(BaseModel):
    """
    Schema for password change requests.
    
    This schema validates password change requests from authenticated users.
    
    Attributes:
        current_password: User's current password
        new_password: New password to set
    """
    
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="New password (minimum 8 characters)"
    )
    
    @validator("new_password")
    def validate_new_password_strength(cls, v: str) -> str:
        """
        Validate new password strength requirements.
        
        Args:
            v: New password string to validate
            
        Returns:
            str: Validated password
            
        Raises:
            ValueError: If password doesn't meet requirements
        """
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, and one digit"
            )
        
        return v
