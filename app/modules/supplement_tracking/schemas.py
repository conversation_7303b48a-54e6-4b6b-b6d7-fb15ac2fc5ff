"""
Supplement tracking Pydantic schemas for request/response validation.

This module defines Pydantic models for API request and response validation,
following PEP 484 type hints and providing comprehensive data validation.
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, field_validator


class SupplementBase(BaseModel):
    """
    Base supplement schema with common fields.
    
    This schema contains fields that are common across different
    supplement-related operations.
    """
    
    name: str = Field(..., min_length=1, max_length=255, description="Supplement name")
    brand: Optional[str] = Field(None, max_length=255, description="Brand or manufacturer")
    description: Optional[str] = Field(None, description="Detailed supplement description")
    category: str = Field(..., max_length=100, description="Supplement category")
    form: str = Field(..., max_length=50, description="Physical form (capsule, tablet, etc.)")
    serving_size: Optional[Decimal] = Field(None, ge=0, description="Standard serving size")
    serving_unit: Optional[str] = Field(None, max_length=20, description="Unit of measurement")
    ingredients: Optional[str] = Field(None, description="List of active ingredients")
    barcode: Optional[str] = Field(None, max_length=50, description="Product barcode")


class SupplementCreate(SupplementBase):
    """
    Schema for supplement creation requests.
    
    This schema is used when creating new supplements in the database.
    """
    
    @field_validator("category")
    @classmethod
    def validate_category(cls, v: str) -> str:
        """
        Validate supplement category.
        
        Args:
            v: Category string to validate
            
        Returns:
            str: Validated category
        """
        valid_categories = [
            "Vitamin", "Mineral", "Amino Acid", "Protein", "Herb", 
            "Probiotic", "Omega-3", "Antioxidant", "Nootropic", "Other"
        ]
        if v not in valid_categories:
            raise ValueError(f"Category must be one of: {', '.join(valid_categories)}")
        return v
    
    @field_validator("form")
    @classmethod
    def validate_form(cls, v: str) -> str:
        """
        Validate supplement form.
        
        Args:
            v: Form string to validate
            
        Returns:
            str: Validated form
        """
        valid_forms = [
            "Capsule", "Tablet", "Powder", "Liquid", "Gummy", 
            "Softgel", "Lozenge", "Spray", "Cream", "Other"
        ]
        if v not in valid_forms:
            raise ValueError(f"Form must be one of: {', '.join(valid_forms)}")
        return v


class SupplementUpdate(BaseModel):
    """
    Schema for supplement update requests.
    
    This schema allows partial updates to supplement information.
    All fields are optional to support partial updates.
    """
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    brand: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    form: Optional[str] = Field(None, max_length=50)
    serving_size: Optional[Decimal] = Field(None, ge=0)
    serving_unit: Optional[str] = Field(None, max_length=20)
    ingredients: Optional[str] = None
    barcode: Optional[str] = Field(None, max_length=50)


class SupplementResponse(SupplementBase):
    """
    Schema for supplement data in API responses.
    
    This schema represents supplement data returned by the API.
    """
    
    id: UUID = Field(..., description="Supplement's unique identifier")
    is_verified: bool = Field(..., description="Whether the supplement data is verified")
    created_at: datetime = Field(..., description="Record creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    created_by_user_id: Optional[UUID] = Field(None, description="User who added this supplement")
    
    class Config:
        """Pydantic configuration for SupplementResponse."""
        
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            Decimal: lambda v: float(v),
        }


class SupplementIntakeBase(BaseModel):
    """
    Base supplement intake schema with common fields.
    """
    
    dosage: Decimal = Field(..., gt=0, description="Amount taken")
    dosage_unit: str = Field(..., max_length=20, description="Unit of measurement for dosage")
    taken_at: Optional[datetime] = Field(None, description="When supplement was consumed")
    notes: Optional[str] = Field(None, description="Optional user notes")
    mood_before: Optional[int] = Field(None, ge=1, le=10, description="Mood rating before (1-10)")
    mood_after: Optional[int] = Field(None, ge=1, le=10, description="Mood rating after (1-10)")
    energy_before: Optional[int] = Field(None, ge=1, le=10, description="Energy level before (1-10)")
    energy_after: Optional[int] = Field(None, ge=1, le=10, description="Energy level after (1-10)")


class SupplementIntakeCreate(SupplementIntakeBase):
    """
    Schema for supplement intake creation requests.
    """
    
    @field_validator("dosage_unit")
    @classmethod
    def validate_dosage_unit(cls, v: str) -> str:
        """
        Validate dosage unit.
        
        Args:
            v: Dosage unit string to validate
            
        Returns:
            str: Validated dosage unit
        """
        valid_units = [
            "mg", "g", "mcg", "IU", "ml", "capsule", "tablet", 
            "scoop", "drop", "spray", "tsp", "tbsp"
        ]
        if v not in valid_units:
            raise ValueError(f"Dosage unit must be one of: {', '.join(valid_units)}")
        return v


class SupplementIntakeResponse(SupplementIntakeBase):
    """
    Schema for supplement intake data in API responses.
    """
    
    id: UUID = Field(..., description="Intake record's unique identifier")
    user_id: UUID = Field(..., description="User's unique identifier")
    supplement_id: UUID = Field(..., description="Supplement's unique identifier")
    created_at: datetime = Field(..., description="Record creation timestamp")
    
    # Include supplement information in response
    supplement: Optional[SupplementResponse] = None
    
    class Config:
        """Pydantic configuration for SupplementIntakeResponse."""
        
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            Decimal: lambda v: float(v),
        }


class SupplementStackBase(BaseModel):
    """
    Base supplement stack schema with common fields.
    """
    
    name: str = Field(..., min_length=1, max_length=255, description="Stack name")
    description: Optional[str] = Field(None, description="Stack description and purpose")


class SupplementStackCreate(SupplementStackBase):
    """
    Schema for supplement stack creation requests.
    """
    pass


class SupplementStackResponse(SupplementStackBase):
    """
    Schema for supplement stack data in API responses.
    """
    
    id: UUID = Field(..., description="Stack's unique identifier")
    user_id: UUID = Field(..., description="User's unique identifier")
    is_active: bool = Field(..., description="Whether the stack is currently being used")
    created_at: datetime = Field(..., description="Stack creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        """Pydantic configuration for SupplementStackResponse."""
        
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
        }


class SupplementSearchParams(BaseModel):
    """
    Schema for supplement search parameters.
    """
    
    search: Optional[str] = Field(None, description="Search term for name, brand, or description")
    category: Optional[str] = Field(None, description="Filter by supplement category")
    skip: int = Field(0, ge=0, description="Number of records to skip")
    limit: int = Field(20, ge=1, le=100, description="Maximum number of records to return")


class SupplementListResponse(BaseModel):
    """
    Schema for paginated supplement list responses.
    """
    
    supplements: List[SupplementResponse] = Field(..., description="List of supplements")
    total: int = Field(..., description="Total number of supplements matching criteria")
    skip: int = Field(..., description="Number of records skipped")
    limit: int = Field(..., description="Maximum number of records returned")
    
    class Config:
        """Pydantic configuration for SupplementListResponse."""
        
        from_attributes = True
