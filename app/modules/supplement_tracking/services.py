"""
Supplement tracking service layer.

This module provides business logic for supplement operations including
CRUD operations, intake tracking, and supplement stack management.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select, and_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.modules.supplement_tracking.models import (
    Supplement, SupplementIntake, SupplementStack
)
from app.modules.supplement_tracking.schemas import (
    SupplementCreate, SupplementUpdate, SupplementIntakeCreate, 
    SupplementStackCreate
)


class SupplementService:
    """
    Service class for supplement tracking operations.
    
    This class encapsulates all business logic related to supplement operations,
    providing a clean interface for controllers and maintaining separation
    of concerns.
    """
    
    def __init__(self, db: AsyncSession):
        """
        Initialize the supplement service.
        
        Args:
            db: Database session for operations
        """
        self.db = db
    
    async def create_supplement(
        self, 
        supplement_data: SupplementCreate,
        created_by_user_id: Optional[UUID] = None
    ) -> Supplement:
        """
        Create a new supplement.
        
        Args:
            supplement_data: Supplement creation data
            created_by_user_id: ID of user creating the supplement
            
        Returns:
            Supplement: Created supplement instance
            
        Raises:
            ValueError: If barcode already exists
        """
        # Check if barcode already exists (if provided)
        if supplement_data.barcode:
            existing_barcode = await self.db.execute(
                select(Supplement).where(Supplement.barcode == supplement_data.barcode)
            )
            if existing_barcode.scalar_one_or_none():
                raise ValueError("Supplement with this barcode already exists")
        
        # Create new supplement
        supplement = Supplement(
            name=supplement_data.name,
            brand=supplement_data.brand,
            description=supplement_data.description,
            category=supplement_data.category,
            form=supplement_data.form,
            serving_size=supplement_data.serving_size,
            serving_unit=supplement_data.serving_unit,
            ingredients=supplement_data.ingredients,
            barcode=supplement_data.barcode,
            created_by_user_id=created_by_user_id,
        )
        
        self.db.add(supplement)
        await self.db.commit()
        await self.db.refresh(supplement)
        
        return supplement
    
    async def get_supplement_by_id(self, supplement_id: UUID) -> Optional[Supplement]:
        """
        Get a supplement by its ID.
        
        Args:
            supplement_id: Supplement's unique identifier
            
        Returns:
            Optional[Supplement]: Supplement instance if found, None otherwise
        """
        result = await self.db.execute(
            select(Supplement).where(Supplement.id == supplement_id)
        )
        return result.scalar_one_or_none()
    
    async def get_supplements(
        self, 
        skip: int = 0, 
        limit: int = 20,
        category: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[Supplement]:
        """
        Get a list of supplements with optional filtering.
        
        Args:
            skip: Number of records to skip (pagination)
            limit: Maximum number of records to return
            category: Filter by supplement category
            search: Search term for name, brand, or description
            
        Returns:
            List[Supplement]: List of supplement instances
        """
        query = select(Supplement)
        
        # Apply filters
        if category:
            query = query.where(Supplement.category == category)
        
        if search:
            search_term = f"%{search}%"
            query = query.where(
                Supplement.name.ilike(search_term) |
                Supplement.brand.ilike(search_term) |
                Supplement.description.ilike(search_term)
            )
        
        # Apply pagination and ordering
        query = query.order_by(Supplement.name).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update_supplement(
        self, 
        supplement_id: UUID, 
        supplement_data: SupplementUpdate
    ) -> Optional[Supplement]:
        """
        Update a supplement's information.
        
        Args:
            supplement_id: Supplement's unique identifier
            supplement_data: Updated supplement data
            
        Returns:
            Optional[Supplement]: Updated supplement instance if found, None otherwise
            
        Raises:
            ValueError: If barcode already exists for another supplement
        """
        supplement = await self.get_supplement_by_id(supplement_id)
        if not supplement:
            return None
        
        # Check for barcode conflicts if barcode is being updated
        if (supplement_data.barcode and 
            supplement_data.barcode != supplement.barcode):
            existing_barcode = await self.db.execute(
                select(Supplement).where(
                    Supplement.barcode == supplement_data.barcode,
                    Supplement.id != supplement_id
                )
            )
            if existing_barcode.scalar_one_or_none():
                raise ValueError("Supplement with this barcode already exists")
        
        # Update fields
        update_data = supplement_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(supplement, field, value)
        
        await self.db.commit()
        await self.db.refresh(supplement)
        
        return supplement
    
    async def delete_supplement(self, supplement_id: UUID) -> bool:
        """
        Delete a supplement.
        
        Args:
            supplement_id: Supplement's unique identifier
            
        Returns:
            bool: True if supplement was deleted, False if not found
        """
        supplement = await self.get_supplement_by_id(supplement_id)
        if not supplement:
            return False
        
        await self.db.delete(supplement)
        await self.db.commit()
        
        return True
    
    async def log_supplement_intake(
        self, 
        user_id: UUID,
        supplement_id: UUID,
        intake_data: SupplementIntakeCreate
    ) -> SupplementIntake:
        """
        Log a supplement intake for a user.
        
        Args:
            user_id: User's unique identifier
            supplement_id: Supplement's unique identifier
            intake_data: Intake logging data
            
        Returns:
            SupplementIntake: Created intake record
            
        Raises:
            ValueError: If supplement not found
        """
        # Verify supplement exists
        supplement = await self.get_supplement_by_id(supplement_id)
        if not supplement:
            raise ValueError("Supplement not found")
        
        # Create intake record
        intake = SupplementIntake(
            user_id=user_id,
            supplement_id=supplement_id,
            dosage=intake_data.dosage,
            dosage_unit=intake_data.dosage_unit,
            taken_at=intake_data.taken_at or datetime.utcnow(),
            notes=intake_data.notes,
            mood_before=intake_data.mood_before,
            mood_after=intake_data.mood_after,
            energy_before=intake_data.energy_before,
            energy_after=intake_data.energy_after,
        )
        
        self.db.add(intake)
        await self.db.commit()
        await self.db.refresh(intake)
        
        return intake
    
    async def get_user_intake_history(
        self, 
        user_id: UUID,
        skip: int = 0,
        limit: int = 50,
        supplement_id: Optional[UUID] = None
    ) -> List[SupplementIntake]:
        """
        Get a user's supplement intake history.
        
        Args:
            user_id: User's unique identifier
            skip: Number of records to skip (pagination)
            limit: Maximum number of records to return
            supplement_id: Filter by specific supplement
            
        Returns:
            List[SupplementIntake]: List of intake records
        """
        query = select(SupplementIntake).options(
            selectinload(SupplementIntake.supplement)
        ).where(SupplementIntake.user_id == user_id)
        
        if supplement_id:
            query = query.where(SupplementIntake.supplement_id == supplement_id)
        
        query = query.order_by(desc(SupplementIntake.taken_at)).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
