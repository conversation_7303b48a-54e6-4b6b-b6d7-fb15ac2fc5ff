# Minimal makefile for Sphinx documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD   ?= sphinx-build
SOURCEDIR     = .
BUILDDIR      = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

# Custom targets for development
.PHONY: clean-build strict linkcheck check open dev info

# Clean build directory
clean-build:
	rm -rf $(BUILDDIR)/*

# Build HTML with warnings as errors
strict:
	@$(SPHINXBUILD) -W -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)

# Check for broken links
linkcheck:
	@$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS) $(O)

# Check documentation for issues
check: linkcheck
	@echo "Documentation check complete"

# Build for production
production: clean-build
	@$(SPHINXBUILD) -W -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo "Production build complete. Files are in $(BUILDDIR)/html/"

# Open documentation in browser
open:
	@if [ -f "$(BUILDDIR)/html/index.html" ]; then \
		if command -v xdg-open > /dev/null; then \
			xdg-open "$(BUILDDIR)/html/index.html"; \
		elif command -v open > /dev/null; then \
			open "$(BUILDDIR)/html/index.html"; \
		else \
			echo "Built documentation is at: $(BUILDDIR)/html/index.html"; \
		fi \
	else \
		echo "Documentation not built. Run 'make html' first."; \
	fi

# Quick development workflow
dev: html open

# Show build info
info:
	@echo "Sphinx build directory: $(BUILDDIR)"
	@echo "Source directory: $(SOURCEDIR)"
	@echo "Sphinx executable: $(SPHINXBUILD)"
	@echo "Sphinx options: $(SPHINXOPTS)"
