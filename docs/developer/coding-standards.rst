================
Coding Standards
================

This document outlines the coding standards and best practices for the Supplement Tracker project.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

We follow industry-standard Python conventions and best practices to ensure code quality, maintainability, and consistency across the project.

**Key Principles**:

- **Readability**: Code should be self-documenting and easy to understand
- **Consistency**: Follow established patterns and conventions
- **Maintainability**: Write code that's easy to modify and extend
- **Testing**: All code should be thoroughly tested
- **Documentation**: Document public APIs and complex logic

Python Style Guide
==================

PEP 8 Compliance

We strictly follow `PEP 8 <https://peps.python.org/pep-0008/>`_ with these specific guidelines:

**Line Length**:
- Maximum line length: 88 characters (Black formatter default)
- Use parentheses for line continuation when possible

**Imports**:
- Group imports in this order: standard library, third-party, local
- Use absolute imports
- One import per line for clarity

.. code-block:: python

    # Standard library
    import asyncio
    import logging
    from datetime import datetime
    from typing import List, Optional
    from uuid import UUID

    # Third-party
    import httpx
    from fastapi import APIRouter, Depends, HTTPException
    from pydantic import BaseModel, Field
    from sqlalchemy.ext.asyncio import AsyncSession

    # Local
    from app.core.database import get_db
    from app.models.user import User
    from app.schemas.user import UserCreate, UserResponse

**Naming Conventions**:

.. code-block:: python

    # Variables and functions: snake_case
    user_count = 10
    def get_user_by_id(user_id: UUID) -> User:
        pass

    # Classes: PascalCase
    class UserService:
        pass

    # Constants: UPPER_SNAKE_CASE
    MAX_RETRY_ATTEMPTS = 3
    DEFAULT_PAGE_SIZE = 20

    # Private attributes: leading underscore
    class UserService:
        def __init__(self):
            self._session = None

Type Hints

Use type hints for all function signatures and class attributes:

.. code-block:: python

    from typing import List, Optional, Dict, Any
    from uuid import UUID

    async def create_user(
        user_data: UserCreate,
        db: AsyncSession,
        current_user: Optional[User] = None
    ) -> UserResponse:
        """Create a new user with proper type hints."""
        pass

    class UserService:
        def __init__(self, db: AsyncSession) -> None:
            self.db: AsyncSession = db
            self._cache: Dict[UUID, User] = {}

Docstrings

Use Google-style docstrings for all public functions and classes:

.. code-block:: python

    def calculate_supplement_dosage(
        supplement: Supplement,
        user_weight: float,
        target_amount: float
    ) -> float:
        """Calculate the appropriate dosage for a supplement.

        Args:
            supplement: The supplement to calculate dosage for
            user_weight: User's weight in kilograms
            target_amount: Target amount in supplement's unit

        Returns:
            Calculated dosage amount

        Raises:
            ValueError: If user_weight is not positive
            SupplementError: If supplement data is invalid

        Example:
            >>> supplement = Supplement(name="Vitamin D3", unit="IU")
            >>> dosage = calculate_supplement_dosage(supplement, 70.0, 5000.0)
            >>> print(f"Take {dosage} IU")
""""""""""""""""""""""""""""""""""""""""""
        if user_weight <= 0:
            raise ValueError("User weight must be positive")
        
        # Implementation here
        return target_amount

Code Organization
=================

Project Structure

Follow the established project structure:

.. code-block:: text

    app/
    ├── core/                    # Core functionality
    │   ├── config.py           # Configuration management
    │   ├── database.py         # Database setup
    │   ├── security.py         # Security utilities
    │   └── exceptions.py       # Custom exceptions
    ├── models/                 # SQLAlchemy models
    │   ├── __init__.py
    │   ├── user.py
    │   └── supplement.py
    ├── schemas/                # Pydantic schemas
    │   ├── __init__.py
    │   ├── user.py
    │   └── supplement.py
    ├── api/                    # API endpoints
    │   └── v1/
    │       ├── __init__.py
    │       ├── api.py          # Main router
    │       └── endpoints/
    │           ├── auth.py
    │           ├── users.py
    │           └── supplements.py
    ├── services/               # Business logic
    │   ├── __init__.py
    │   ├── user_service.py
    │   └── supplement_service.py
    └── tests/                  # Test files
        ├── conftest.py
        ├── test_models/
        ├── test_api/
        └── test_services/

Module Organization

**Each module should have a clear, single responsibility**:

.. code-block:: python

    # user_service.py - User business logic
    class UserService:
        """Service for user-related business logic."""
        
        def __init__(self, db: AsyncSession):
            self.db = db
        
        async def create_user(self, user_data: UserCreate) -> User:
            """Create a new user."""
            pass
        
        async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
            """Get user by ID."""
            pass

**Import organization within modules**:

.. code-block:: python

    """User service module.
    
    This module contains business logic for user management operations.
"""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""

    # Standard library imports first
    import logging
    from typing import List, Optional
    from uuid import UUID

    # Third-party imports
    from sqlalchemy.ext.asyncio import AsyncSession
    from sqlalchemy import select

    # Local imports last
    from app.models.user import User
    from app.schemas.user import UserCreate, UserUpdate
    from app.core.exceptions import UserNotFoundError

    logger = logging.getLogger(__name__)

FastAPI Best Practices
======================

Endpoint Design

**Use proper HTTP methods and status codes**:

.. code-block:: python

    from fastapi import APIRouter, HTTPException, status
    from fastapi.responses import JSONResponse

    router = APIRouter(prefix="/api/v1/users", tags=["users"])

    @router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
    async def create_user(
        user_data: UserCreate,
        db: AsyncSession = Depends(get_db)
    ) -> UserResponse:
        """Create a new user account."""
        try:
            user = await user_service.create_user(user_data, db)
            return UserResponse.from_orm(user)
        except EmailAlreadyExistsError:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Email already registered"
            )

**Dependency Injection**:

.. code-block:: python

    from fastapi import Depends
    from app.core.database import get_db
    from app.core.security import get_current_user

    async def get_user_service(
        db: AsyncSession = Depends(get_db)
    ) -> UserService:
        """Get user service instance."""
        return UserService(db)

    @router.get("/me", response_model=UserResponse)
    async def get_current_user_profile(
        current_user: User = Depends(get_current_user),
        user_service: UserService = Depends(get_user_service)
    ) -> UserResponse:
        """Get current user's profile."""
        return UserResponse.from_orm(current_user)

Error Handling

**Use custom exceptions and proper error responses**:

.. code-block:: python

    # exceptions.py
    class SupplementTrackerException(Exception):
        """Base exception for the application."""
        pass

    class UserNotFoundError(SupplementTrackerException):
        """Raised when a user is not found."""
        pass

    class ValidationError(SupplementTrackerException):
        """Raised when validation fails."""
        pass

    # In endpoints
    @router.get("/{user_id}", response_model=UserResponse)
    async def get_user(
        user_id: UUID,
        user_service: UserService = Depends(get_user_service)
    ) -> UserResponse:
        """Get user by ID."""
        try:
            user = await user_service.get_user_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            return UserResponse.from_orm(user)
        except ValidationError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )

Database Best Practices
=======================

SQLAlchemy Models

**Use proper model definitions**:

.. code-block:: python

    from sqlalchemy import Column, String, Boolean, DateTime, Text
    from sqlalchemy.dialects.postgresql import UUID
    from sqlalchemy.sql import func
    import uuid

    from app.core.database import Base

    class User(Base):
        """User model."""
        
        __tablename__ = "users"

        id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
        email = Column(String(255), unique=True, nullable=False, index=True)
        username = Column(String(50), unique=True, nullable=False, index=True)
        hashed_password = Column(String(255), nullable=False)
        full_name = Column(String(255), nullable=True)
        bio = Column(Text, nullable=True)
        is_active = Column(Boolean, default=True, nullable=False)
        is_verified = Column(Boolean, default=False, nullable=False)
        created_at = Column(DateTime(timezone=True), server_default=func.now())
        updated_at = Column(DateTime(timezone=True), onupdate=func.now())

        def __repr__(self) -> str:
            return f"<User(id={self.id}, username='{self.username}')>"

**Query patterns**:

.. code-block:: python

    async def get_active_users(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """Get active users with pagination."""
        result = await db.execute(
            select(User)
            .where(User.is_active == True)
            .offset(skip)
            .limit(limit)
            .order_by(User.created_at.desc())
        )
        return result.scalars().all()

Pydantic Schemas

**Use proper schema definitions**:

.. code-block:: python

    from pydantic import BaseModel, EmailStr, Field, validator
    from typing import Optional
    from datetime import datetime
    from uuid import UUID

    class UserBase(BaseModel):
        """Base user schema."""
        email: EmailStr
        username: str = Field(..., min_length=3, max_length=50)
        full_name: Optional[str] = Field(None, max_length=255)
        bio: Optional[str] = Field(None, max_length=1000)

    class UserCreate(UserBase):
        """Schema for creating a user."""
        password: str = Field(..., min_length=8, max_length=128)

        @validator('password')
        def validate_password(cls, v):
            """Validate password strength."""
            if not any(c.isupper() for c in v):
                raise ValueError('Password must contain uppercase letter')
            if not any(c.islower() for c in v):
                raise ValueError('Password must contain lowercase letter')
            if not any(c.isdigit() for c in v):
                raise ValueError('Password must contain digit')
            return v

    class UserResponse(UserBase):
        """Schema for user responses."""
        id: UUID
        is_active: bool
        is_verified: bool
        created_at: datetime
        updated_at: Optional[datetime]

        class Config:
            orm_mode = True

Testing Standards
=================

Test Structure

**Organize tests to mirror the application structure**:

.. code-block:: text

    tests/
    ├── conftest.py              # Shared fixtures
    ├── test_models/
    │   ├── test_user.py
    │   └── test_supplement.py
    ├── test_api/
    │   ├── test_auth.py
    │   ├── test_users.py
    │   └── test_supplements.py
    ├── test_services/
    │   ├── test_user_service.py
    │   └── test_supplement_service.py
    └── test_core/
        ├── test_security.py
        └── test_database.py

**Test naming conventions**:

.. code-block:: python

    class TestUserService:
        """Test user service functionality."""

        async def test_create_user_success(self, db_session, user_service):
            """Test successful user creation."""
            pass

        async def test_create_user_duplicate_email_raises_error(
            self, db_session, user_service
        ):
            """Test that creating user with duplicate email raises error."""
            pass

        async def test_get_user_by_id_returns_user_when_exists(
            self, db_session, user_service, sample_user
        ):
            """Test getting user by ID returns user when it exists."""
            pass

        async def test_get_user_by_id_returns_none_when_not_exists(
            self, db_session, user_service
        ):
            """Test getting user by ID returns None when user doesn't exist."""
            pass

**Test implementation**:

.. code-block:: python

    import pytest
    from uuid import uuid4
    from app.services.user_service import UserService
    from app.schemas.user import UserCreate
    from app.core.exceptions import EmailAlreadyExistsError

    class TestUserService:
        async def test_create_user_success(self, db_session):
            """Test successful user creation."""
            # Arrange
            user_service = UserService(db_session)
            user_data = UserCreate(
                email="<EMAIL>",
                username="testuser",
                password="SecurePass123!",
                full_name="Test User"
            )

            # Act
            user = await user_service.create_user(user_data)

            # Assert
            assert user.email == "<EMAIL>"
            assert user.username == "testuser"
            assert user.full_name == "Test User"
            assert user.is_active is True
            assert user.is_verified is False
            assert user.id is not None

        async def test_create_user_duplicate_email_raises_error(
            self, db_session, sample_user
        ):
            """Test that creating user with duplicate email raises error."""
            # Arrange
            user_service = UserService(db_session)
            user_data = UserCreate(
                email=sample_user.email,  # Same email as existing user
                username="newuser",
                password="SecurePass123!"
            )

            # Act & Assert
            with pytest.raises(EmailAlreadyExistsError):
                await user_service.create_user(user_data)

Code Quality Tools
==================

Automated Formatting

**Black**: Automatic code formatting

.. code-block:: bash

    # Format all Python files
    black app/ tests/

    # Check formatting without making changes
    black --check app/ tests/

**isort**: Import sorting

.. code-block:: bash

    # Sort imports
    isort app/ tests/

    # Check import sorting
    isort --check-only app/ tests/

Linting

**flake8**: Code linting

.. code-block:: bash

    # Run linting
    flake8 app/ tests/

**mypy**: Type checking

.. code-block:: bash

    # Run type checking
    mypy app/

Pre-commit Hooks

Use pre-commit hooks to ensure code quality:

.. code-block:: yaml

    # .pre-commit-config.yaml
    repos:
      - repo: https://github.com/psf/black
        rev: 23.3.0
        hooks:
          - id: black
      - repo: https://github.com/pycqa/isort
        rev: 5.12.0
        hooks:
          - id: isort
      - repo: https://github.com/pycqa/flake8
        rev: 6.0.0
        hooks:
          - id: flake8
      - repo: https://github.com/pre-commit/mirrors-mypy
        rev: v1.3.0
        hooks:
          - id: mypy

Security Best Practices
=======================

Input Validation

**Always validate and sanitize input**:

.. code-block:: python

    from pydantic import BaseModel, validator, Field
    import re

    class SupplementCreate(BaseModel):
        name: str = Field(..., min_length=1, max_length=255)
        description: Optional[str] = Field(None, max_length=2000)
        
        @validator('name')
        def validate_name(cls, v):
            # Remove any potentially dangerous characters
            if re.search(r'[<>"\']', v):
                raise ValueError('Name contains invalid characters')
            return v.strip()

**Use parameterized queries** (SQLAlchemy handles this automatically):

.. code-block:: python

    # Good - SQLAlchemy handles parameterization
    result = await db.execute(
        select(User).where(User.email == email)
    )

    # Never do this - SQL injection risk
    # query = f"SELECT * FROM users WHERE email = '{email}'"

Authentication & Authorization

**Implement proper authentication checks**:

.. code-block:: python

    from app.core.security import verify_token, get_current_user

    @router.get("/protected-endpoint")
    async def protected_endpoint(
        current_user: User = Depends(get_current_user)
    ):
        """Endpoint that requires authentication."""
        return {"message": f"Hello, {current_user.username}"}

**Use role-based access control**:

.. code-block:: python

    from app.core.security import require_permission

    @router.delete("/admin-only")
    async def admin_only_endpoint(
        current_user: User = Depends(require_permission("admin"))
    ):
        """Endpoint that requires admin permission."""
        pass

Performance Guidelines
======================

Database Optimization

**Use appropriate indexes**:

.. code-block:: python

    class User(Base):
        __tablename__ = "users"
        
        id = Column(UUID(as_uuid=True), primary_key=True)
        email = Column(String(255), unique=True, index=True)  # Indexed
        username = Column(String(50), unique=True, index=True)  # Indexed
        created_at = Column(DateTime(timezone=True), index=True)  # For sorting

**Use eager loading when appropriate**:

.. code-block:: python

    from sqlalchemy.orm import selectinload

    # Load user with related data in one query
    result = await db.execute(
        select(User)
        .options(selectinload(User.supplement_intakes))
        .where(User.id == user_id)
    )

**Implement pagination**:

.. code-block:: python

    async def get_supplements_paginated(
        db: AsyncSession,
        page: int = 1,
        per_page: int = 20
    ) -> Dict[str, Any]:
        """Get paginated supplements."""
        offset = (page - 1) * per_page
        
        # Get total count
        count_result = await db.execute(select(func.count(Supplement.id)))
        total = count_result.scalar()
        
        # Get paginated results
        result = await db.execute(
            select(Supplement)
            .offset(offset)
            .limit(per_page)
            .order_by(Supplement.created_at.desc())
        )
        supplements = result.scalars().all()
        
        return {
            "data": supplements,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": (total + per_page - 1) // per_page
            }
        }

Async Best Practices

**Use async/await properly**:

.. code-block:: python

    # Good - proper async usage
    async def process_user_data(user_id: UUID) -> Dict[str, Any]:
        async with httpx.AsyncClient() as client:
            user_data = await get_user_data(user_id)
            external_data = await client.get(f"https://api.example.com/users/{user_id}")
            
            return {
                "user": user_data,
                "external": external_data.json()
            }

    # Good - concurrent processing
    async def process_multiple_users(user_ids: List[UUID]) -> List[Dict[str, Any]]:
        tasks = [process_user_data(user_id) for user_id in user_ids]
        return await asyncio.gather(*tasks)

Documentation Standards
=======================

Code Documentation

**Document all public APIs**:

.. code-block:: python

    class SupplementService:
        """Service for supplement-related operations.
        
        This service handles all business logic related to supplements,
        including creation, retrieval, updating, and deletion.
        
        Attributes:
            db: Database session for operations
"""""""""""""""""""""""""""""""""""""""""""""""

        def __init__(self, db: AsyncSession) -> None:
            """Initialize the supplement service.
            
            Args:
                db: Database session for operations
"""""""""""""""""""""""""""""""""""""""""""""""""""
            self.db = db

        async def create_supplement(
            self,
            supplement_data: SupplementCreate,
            created_by: UUID
        ) -> Supplement:
            """Create a new supplement.
            
            Args:
                supplement_data: Data for the new supplement
                created_by: ID of the user creating the supplement
                
            Returns:
                The created supplement
                
            Raises:
                ValidationError: If supplement data is invalid
                DuplicateSupplementError: If supplement already exists
""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
            pass

**Use type hints and docstrings together**:

.. code-block:: python

    from typing import List, Optional, Dict, Any

    async def analyze_supplement_interactions(
        supplements: List[Supplement],
        user_conditions: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Analyze potential interactions between supplements.
        
        This function checks for known interactions between the provided
        supplements and considers any user medical conditions.
        
        Args:
            supplements: List of supplements to analyze
            user_conditions: Optional list of user medical conditions
            
        Returns:
            Dictionary containing:
                - interactions: List of potential interactions
                - warnings: List of warnings
                - recommendations: List of recommendations
                
        Example:
            >>> supplements = [vitamin_d, calcium]
            >>> result = await analyze_supplement_interactions(supplements)
            >>> print(result['interactions'])
            [{'type': 'positive', 'description': 'Enhanced absorption'}]
""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
        pass

Related Documentation
=====================

- :doc:`setup` - Development environment setup
- :doc:`testing` - Testing guidelines and practices
- :doc:`contributing` - Contributing to the project
- :doc:`../architecture/overview` - System architecture overview
