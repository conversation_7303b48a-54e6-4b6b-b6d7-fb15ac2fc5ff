===============================================
Developer Setup Guide
===============================================

This guide helps developers set up a local development environment for contributing to the Supplement Tracker project.

.. contents:: Table of Contents
   :local:
   :depth: 2

Prerequisites
=============

System Requirements
-------------------

- **Operating System**: Linux (Ubuntu 20.04+), macOS (10.15+), or Windows 10+ with WSL2
- **Memory**: 8GB RAM (16GB recommended)
- **Storage**: 20GB free disk space
- **Network**: Internet connection for package downloads

Required Software
-----------------

**Option 1: Nix (Recommended)**

.. code-block:: bash

    # Install Nix package manager
    curl -L https://nixos.org/nix/install | sh
    
    # Reload shell
    source ~/.bashrc
    
    # Enable flakes (optional but recommended)
    mkdir -p ~/.config/nix
    echo "experimental-features = nix-command flakes" >> ~/.config/nix/nix.conf

**Option 2: Manual Installation**

- Python 3.11+
- PostgreSQL 13+
- Redis 6+
- Git
- Node.js 18+ (for frontend development)

Development Environment Setup
============================

Clone Repository
----------------

.. code-block:: bash

    # Clone the repository
    git clone https://github.com/forkrul/day2-supplement-tracker.git
    cd day2-supplement-tracker
    
    # Create a new branch for your work
    git checkout -b feature/your-feature-name

Nix Development Environment
--------------------------

.. code-block:: bash

    # Enter the development shell
    nix-shell
    
    # You should see a welcome message with available commands
    # The environment includes Python, PostgreSQL, Redis, and all dependencies

Manual Environment Setup
------------------------

**Create Python Virtual Environment**:

.. code-block:: bash

    # Create virtual environment
    python3.11 -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    pip install -r requirements.txt
    pip install -r requirements-dev.txt

**Install and Start Services**:

.. code-block:: bash

    # Install PostgreSQL (Ubuntu/Debian)
    sudo apt update
    sudo apt install postgresql postgresql-contrib
    
    # Install Redis
    sudo apt install redis-server
    
    # Start services
    sudo systemctl start postgresql
    sudo systemctl start redis-server

Database Setup
==============

Create Development Database
---------------------------

.. code-block:: bash

    # Connect to PostgreSQL as superuser
    sudo -u postgres psql
    
    # Create database and user
    CREATE DATABASE supplement_tracker_dev;
    CREATE USER supplement_dev WITH PASSWORD 'dev_password';
    GRANT ALL PRIVILEGES ON DATABASE supplement_tracker_dev TO supplement_dev;
    
    # Exit PostgreSQL
    \q

Configure Environment Variables
------------------------------

Create a `.env.development` file:

.. code-block:: bash

    # Copy example environment file
    cp .env.example .env.development

Edit `.env.development`:

.. code-block:: bash

    # Development Configuration
    DEBUG=true
    TESTING=false
    LOG_LEVEL=DEBUG
    
    # Database
    DATABASE_URL=postgresql://supplement_dev:dev_password@localhost:5432/supplement_tracker_dev
    
    # Redis
    REDIS_URL=redis://localhost:6379/0
    
    # Security (generate with: openssl rand -hex 32)
    SECRET_KEY=your-development-secret-key-here
    ACCESS_TOKEN_EXPIRE_MINUTES=60
    
    # CORS (allow local development)
    CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://127.0.0.1:3000"]
    
    # Email (console backend for development)
    EMAIL_BACKEND=console
    
    # Disable external services in development
    SENTRY_DSN=
    METRICS_ENABLED=false

Run Database Migrations
-----------------------

.. code-block:: bash

    # Load environment variables
    export $(cat .env.development | grep -v '^#' | xargs)
    
    # Run migrations
    alembic upgrade head
    
    # Verify tables were created
    psql $DATABASE_URL -c "\dt"

Create Test Data (Optional)
---------------------------

.. code-block:: bash

    # Run the seed script to create sample data
    python scripts/seed_data.py
    
    # Or create a superuser
    python scripts/create_superuser.py

Development Workflow
===================

Running the Application
----------------------

**Using Make Commands** (Recommended):

.. code-block:: bash

    # Start development server with auto-reload
    make dev
    
    # Run in debug mode
    make dev-debug
    
    # Run tests
    make test
    
    # Run tests with coverage
    make test-coverage
    
    # Format code
    make format
    
    # Run linting
    make lint
    
    # Run type checking
    make typecheck

**Manual Commands**:

.. code-block:: bash

    # Load environment
    export $(cat .env.development | grep -v '^#' | xargs)
    
    # Start development server
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    
    # Or using Python directly
    python -m app.main

**Verify Installation**:

Open your browser and navigate to:

- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

Code Quality Tools
==================

Pre-commit Hooks
----------------

Install pre-commit hooks to ensure code quality:

.. code-block:: bash

    # Install pre-commit
    pip install pre-commit
    
    # Install hooks
    pre-commit install
    
    # Run hooks on all files (optional)
    pre-commit run --all-files

The pre-commit configuration includes:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pytest**: Running tests

Code Formatting
---------------

.. code-block:: bash

    # Format code with Black
    black app/ tests/
    
    # Sort imports with isort
    isort app/ tests/
    
    # Or use the make command
    make format

Linting
-------

.. code-block:: bash

    # Run flake8 linting
    flake8 app/ tests/
    
    # Run mypy type checking
    mypy app/
    
    # Or use the make command
    make lint

Testing
=======

Test Structure
--------------

.. code-block:: text

    tests/
    ├── conftest.py              # Pytest configuration and fixtures
    ├── test_main.py             # Main application tests
    ├── api/                     # API endpoint tests
    │   ├── test_auth.py
    │   ├── test_users.py
    │   ├── test_supplements.py
    │   └── test_intakes.py
    ├── core/                    # Core functionality tests
    │   ├── test_config.py
    │   ├── test_database.py
    │   └── test_security.py
    ├── modules/                 # Module-specific tests
    │   ├── test_user_management.py
    │   └── test_supplement_tracking.py
    └── utils/                   # Utility function tests
        └── test_helpers.py

Running Tests
------------

.. code-block:: bash

    # Run all tests
    pytest
    
    # Run with coverage
    pytest --cov=app --cov-report=html --cov-report=term
    
    # Run specific test file
    pytest tests/api/test_auth.py
    
    # Run specific test
    pytest tests/api/test_auth.py::test_user_registration
    
    # Run tests with verbose output
    pytest -v
    
    # Run tests in parallel (if pytest-xdist is installed)
    pytest -n auto

Test Configuration
-----------------

Tests use a separate test database. Create `.env.test`:

.. code-block:: bash

    # Test Configuration
    TESTING=true
    DEBUG=false
    LOG_LEVEL=WARNING
    
    # Test Database
    DATABASE_URL=postgresql://supplement_dev:dev_password@localhost:5432/supplement_tracker_test
    
    # Test Redis (separate database)
    REDIS_URL=redis://localhost:6379/1
    
    # Test-specific settings
    SECRET_KEY=test-secret-key-not-for-production
    ACCESS_TOKEN_EXPIRE_MINUTES=1

Create test database:

.. code-block:: bash

    # Create test database
    sudo -u postgres createdb supplement_tracker_test
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE supplement_tracker_test TO supplement_dev;"

Writing Tests
------------

**Example Test File**:

.. code-block:: python

    # tests/api/test_supplements.py
    import pytest
    from httpx import AsyncClient
    from app.main import app
    from tests.conftest import TestClient

    class TestSupplements:
        async def test_list_supplements(self, client: TestClient):
            """Test listing supplements"""
            response = await client.get("/api/v1/supplements")
            assert response.status_code == 200
            data = response.json()
            assert "data" in data
            assert isinstance(data["data"], list)

        async def test_create_supplement(self, client: TestClient, auth_headers):
            """Test creating a supplement"""
            supplement_data = {
                "name": "Test Vitamin",
                "category": "Vitamin",
                "form": "Capsule"
            }
            
            response = await client.post(
                "/api/v1/supplements",
                json=supplement_data,
                headers=auth_headers
            )
            
            assert response.status_code == 201
            data = response.json()
            assert data["data"]["name"] == "Test Vitamin"

        async def test_get_supplement_not_found(self, client: TestClient):
            """Test getting non-existent supplement"""
            response = await client.get("/api/v1/supplements/nonexistent-id")
            assert response.status_code == 404

**Test Fixtures**:

.. code-block:: python

    # tests/conftest.py
    import pytest
    import asyncio
    from httpx import AsyncClient
    from app.main import app
    from app.core.database import get_db
    from app.core.config import settings

    @pytest.fixture(scope="session")
    def event_loop():
        """Create an instance of the default event loop for the test session."""
        loop = asyncio.get_event_loop_policy().new_event_loop()
        yield loop
        loop.close()

    @pytest.fixture
    async def client():
        """Create test client"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            yield ac

    @pytest.fixture
    async def auth_headers(client):
        """Create authenticated user and return auth headers"""
        # Register test user
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "TestPass123!"
        }
        await client.post("/api/v1/auth/register", json=user_data)
        
        # Login and get token
        login_response = await client.post("/api/v1/auth/login", json={
            "username": "testuser",
            "password": "TestPass123!"
        })
        token = login_response.json()["access_token"]
        
        return {"Authorization": f"Bearer {token}"}

Database Migrations
==================

Creating Migrations
-------------------

.. code-block:: bash

    # Create a new migration
    alembic revision --autogenerate -m "Add new table"
    
    # Review the generated migration file in alembic/versions/
    # Edit if necessary
    
    # Apply the migration
    alembic upgrade head

Migration Best Practices
-----------------------

1. **Review generated migrations**: Always check auto-generated migrations
2. **Test migrations**: Test both upgrade and downgrade
3. **Data migrations**: Handle data transformations carefully
4. **Backup data**: Always backup before running migrations in production

.. code-block:: python

    # Example migration with data transformation
    from alembic import op
    import sqlalchemy as sa

    def upgrade():
        # Add new column
        op.add_column('supplements', sa.Column('new_field', sa.String(255)))
        
        # Migrate existing data
        connection = op.get_bind()
        connection.execute(
            "UPDATE supplements SET new_field = 'default_value' WHERE new_field IS NULL"
        )
        
        # Make column non-nullable
        op.alter_column('supplements', 'new_field', nullable=False)

    def downgrade():
        op.drop_column('supplements', 'new_field')

API Documentation
================

Interactive Documentation
-------------------------

The API documentation is automatically generated from code:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

Updating Documentation
---------------------

Documentation is generated from:

1. **Pydantic models**: Request/response schemas
2. **Docstrings**: Endpoint descriptions
3. **Type hints**: Parameter types and validation

.. code-block:: python

    from fastapi import APIRouter, Depends
    from app.schemas.supplement import SupplementCreate, SupplementResponse

    @router.post("/supplements", response_model=SupplementResponse)
    async def create_supplement(
        supplement: SupplementCreate,
        current_user: User = Depends(get_current_user)
    ):
        """
        Create a new supplement.
        
        - **name**: Supplement name (required)
        - **category**: Supplement category (required)
        - **brand**: Brand name (optional)
        - **description**: Detailed description (optional)
        
        Returns the created supplement with generated ID.
        """
        # Implementation here
        pass

Debugging
=========

Debug Configuration
------------------

For debugging, use the debug configuration in `.env.development`:

.. code-block:: bash

    DEBUG=true
    LOG_LEVEL=DEBUG

**VS Code Debug Configuration** (`.vscode/launch.json`):

.. code-block:: json

    {
        "version": "0.2.0",
        "configurations": [
            {
                "name": "FastAPI Debug",
                "type": "python",
                "request": "launch",
                "program": "${workspaceFolder}/app/main.py",
                "console": "integratedTerminal",
                "envFile": "${workspaceFolder}/.env.development",
                "args": []
            }
        ]
    }

Logging
-------

Configure logging for development:

.. code-block:: python

    import logging
    import sys

    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/development.log')
        ]
    )

    logger = logging.getLogger(__name__)

Database Debugging
-----------------

Enable SQL query logging:

.. code-block:: bash

    # In .env.development
    DATABASE_ECHO=true

This will log all SQL queries to the console.

Performance Profiling
--------------------

.. code-block:: python

    import cProfile
    import pstats

    # Profile a function
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Your code here
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative').print_stats(10)

Contributing Guidelines
======================

Code Style
----------

Follow the project's coding standards:

- **PEP 8**: Python style guide
- **PEP 257**: Docstring conventions
- **PEP 484**: Type hints
- **Black**: Code formatting
- **isort**: Import sorting

Commit Messages
--------------

Use conventional commit format:

.. code-block:: text

    type(scope): description

    [optional body]

    [optional footer]

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples**:

.. code-block:: text

    feat(api): add supplement search endpoint
    
    fix(auth): handle token expiration correctly
    
    docs(api): update authentication documentation

Pull Request Process
-------------------

1. **Create feature branch**: `git checkout -b feature/your-feature`
2. **Make changes**: Follow coding standards
3. **Add tests**: Ensure good test coverage
4. **Update documentation**: Update relevant docs
5. **Run tests**: Ensure all tests pass
6. **Create PR**: Use the PR template
7. **Code review**: Address review feedback
8. **Merge**: Squash and merge when approved

Troubleshooting
==============

Common Issues
------------

**Database Connection Error**:

.. code-block:: bash

    # Check PostgreSQL status
    sudo systemctl status postgresql
    
    # Test connection
    psql $DATABASE_URL -c "SELECT version();"

**Redis Connection Error**:

.. code-block:: bash

    # Check Redis status
    sudo systemctl status redis-server
    
    # Test connection
    redis-cli ping

**Import Errors**:

.. code-block:: bash

    # Check Python path
    python -c "import sys; print('\n'.join(sys.path))"
    
    # Reinstall dependencies
    pip install -r requirements.txt --force-reinstall

**Port Already in Use**:

.. code-block:: bash

    # Find process using port 8000
    lsof -i :8000
    
    # Kill the process
    kill -9 <PID>

Getting Help
-----------

- **Documentation**: Check this guide and API docs
- **GitHub Issues**: Search existing issues
- **Discussions**: Use GitHub Discussions for questions
- **Code Review**: Ask for help in pull requests

Next Steps
==========

After setting up your development environment:

1. **Explore the codebase**: :doc:`coding-standards`
2. **Run tests**: :doc:`testing`
3. **Make your first contribution**: :doc:`contributing`
4. **Review architecture**: :doc:`../architecture/overview`

For deployment information:

- :doc:`../architecture/deployment`
- :doc:`../user-guide/installation`
