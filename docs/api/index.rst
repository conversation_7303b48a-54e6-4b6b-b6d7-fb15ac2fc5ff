===============================================
API Reference
API Reference
===============================================

Welcome to the Supplement Tracker API reference documentation. This section provides comprehensive information about all API endpoints, schemas, and usage patterns.

.. contents:: Table of Contents
   :local:
   :depth: 2

API Overview
============

The Supplement Tracker API is a RESTful API built with FastAPI that provides:

- **User Management**: Registration, authentication, and profile management
- **Supplement Tracking**: CRUD operations for supplements and intake logging
- **Community Features**: Social interactions and peer review (planned)
- **Research Tools**: Data analysis and export capabilities (planned)

**Base URL**: ``https://api.supplementtracker.com/api/v1``

**Authentication**: JWT Bearer tokens

**Content Type**: ``application/json``

Quick Start
===========

1. **Register a User**:

.. code-block:: bash

    curl -X POST "https://api.supplementtracker.com/api/v1/auth/register" \
         -H "Content-Type: application/json" \
         -d '{
           "email": "<EMAIL>",
           "username": "testuser",
           "password": "SecurePass123!",
           "full_name": "Test User"
         }'

2. **Login**:

.. code-block:: bash

    curl -X POST "https://api.supplementtracker.com/api/v1/auth/login" \
         -H "Content-Type: application/json" \
         -d '{
           "username": "testuser",
           "password": "SecurePass123!"
         }'

3. **Use the API**:

.. code-block:: bash

    curl -X GET "https://api.supplementtracker.com/api/v1/users/me" \
         -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

Interactive Documentation
=========================

The API provides interactive documentation:

- **Swagger UI**: https://api.supplementtracker.com/docs
- **ReDoc**: https://api.supplementtracker.com/redoc
- **OpenAPI Spec**: https://api.supplementtracker.com/openapi.json

API Endpoints
=============

Authentication
--------------

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Endpoint
     - Method
     - Description
   * - ``/auth/register``
     - POST
     - Register a new user account
   * - ``/auth/login``
     - POST
     - Login and receive access tokens
   * - ``/auth/refresh``
     - POST
     - Refresh access token
   * - ``/auth/logout``
     - POST
     - Logout and invalidate tokens
   * - ``/auth/password-reset-request``
     - POST
     - Request password reset email
   * - ``/auth/password-reset``
     - POST
     - Reset password with token
   * - ``/auth/password``
     - PUT
     - Change password
   * - ``/auth/permissions``
     - GET
     - Get user permissions

User Management
---------------

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Endpoint
     - Method
     - Description
   * - ``/users/me``
     - GET
     - Get current user profile
   * - ``/users/me``
     - PUT
     - Update current user profile
   * - ``/users/me``
     - DELETE
     - Delete current user account
   * - ``/users/me/stats``
     - GET
     - Get user statistics
   * - ``/users/me/preferences``
     - GET
     - Get user preferences
   * - ``/users/me/preferences``
     - PUT
     - Update user preferences
   * - ``/users/me/export``
     - GET
     - Export user data
   * - ``/users/me/sessions``
     - GET
     - Get active sessions
   * - ``/users/me/sessions/{id}``
     - DELETE
     - Revoke specific session

Supplements
-----------

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Endpoint
     - Method
     - Description
   * - ``/supplements``
     - GET
     - List supplements with filtering
   * - ``/supplements``
     - POST
     - Create a new supplement
   * - ``/supplements/{id}``
     - GET
     - Get supplement by ID
   * - ``/supplements/{id}``
     - PUT
     - Update supplement
   * - ``/supplements/search``
     - GET
     - Advanced supplement search
   * - ``/supplements/categories``
     - GET
     - Get supplement categories
   * - ``/supplements/{id}/verify``
     - POST
     - Verify supplement information
   * - ``/supplements/{id}/verifications``
     - GET
     - Get verification history

Supplement Intakes
------------------
-----------------

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Endpoint
     - Method
     - Description
   * - ``/intakes``
     - POST
     - Log supplement intake
   * - ``/intakes/{id}``
     - GET
     - Get intake by ID
   * - ``/intakes/{id}``
     - PUT
     - Update intake record
   * - ``/intakes/{id}``
     - DELETE
     - Delete intake record
   * - ``/intakes/history``
     - GET
     - Get intake history
   * - ``/intakes/daily``
     - GET
     - Get daily intake summary
   * - ``/intakes/export``
     - GET
     - Export intake data

Supplement Stacks
-----------------
----------------

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Endpoint
     - Method
     - Description
   * - ``/stacks``
     - GET
     - List user's supplement stacks
   * - ``/stacks``
     - POST
     - Create a new stack
   * - ``/stacks/{id}``
     - GET
     - Get stack by ID
   * - ``/stacks/{id}``
     - PUT
     - Update stack
   * - ``/stacks/{id}``
     - DELETE
     - Delete stack
   * - ``/stacks/{id}/items``
     - POST
     - Add item to stack
   * - ``/stacks/{id}/items/{item_id}``
     - PUT
     - Update stack item
   * - ``/stacks/{id}/items/{item_id}``
     - DELETE
     - Remove item from stack

Response Format
===============
==============

Standard Response
-----------------

All successful API responses follow this format:

.. code-block:: json

    {
        "data": {
            // Response data here
        },
        "meta": {
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

Paginated Response
------------------
-----------------

List endpoints include pagination information:

.. code-block:: json

    {
        "data": [
            // Array of items
        ],
        "pagination": {
            "total": 150,
            "page": 1,
            "per_page": 20,
            "pages": 8
        },
        "meta": {
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

Error Response
--------------

Error responses follow this format:

.. code-block:: json

    {
        "error": {
            "code": "ERROR_CODE",
            "message": "Human-readable error message",
            "details": {
                "field": "specific_field",
                "issue": "detailed_issue_description"
            },
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

Authentication
==============
=============

The API uses JWT (JSON Web Tokens) for authentication. Include the access token in the Authorization header:

.. code-block:: text

    Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

**Token Types**:

- **Access Token**: Short-lived (15 minutes), used for API requests
- **Refresh Token**: Long-lived (7 days), used to obtain new access tokens

Rate Limiting
=============
============

The API implements rate limiting to ensure fair usage:

.. list-table::
   :header-rows: 1
   :widths: 25 75

   * - User Type
     - Rate Limit
   * - Anonymous
     - 100 requests per hour
   * - Authenticated
     - 1,000 requests per hour
   * - Premium
     - 5,000 requests per hour
   * - API Key
     - 10,000 requests per hour

Rate limit information is included in response headers:

.. code-block:: text

    X-RateLimit-Limit: 1000
    X-RateLimit-Remaining: 999
    X-RateLimit-Reset: 1640995200

Versioning
==========
=========

The API uses URL versioning:

- **Current Version**: ``/api/v1/``
- **Future Versions**: ``/api/v2/``, etc.

**Backward Compatibility**:

- Additive changes (new fields, endpoints) are non-breaking
- Breaking changes require a new API version
- Deprecated features have a 6-month notice period

Status Codes
============
===========

The API uses standard HTTP status codes:

**Success (2xx)**:
- ``200 OK``: Request successful
- ``201 Created``: Resource created
- ``204 No Content``: Successful, no response body

**Client Error (4xx)**:
- ``400 Bad Request``: Invalid request data
- ``401 Unauthorized``: Authentication required
- ``403 Forbidden``: Insufficient permissions
- ``404 Not Found``: Resource not found
- ``409 Conflict``: Resource conflict
- ``429 Too Many Requests``: Rate limit exceeded

**Server Error (5xx)**:
- ``500 Internal Server Error``: Unexpected server error
- ``503 Service Unavailable``: Service temporarily unavailable

SDKs and Libraries
==================

Official SDKs
-------------

**Python SDK** (Planned):

.. code-block:: bash

    pip install supplement-tracker-sdk

.. code-block:: python

    from supplement_tracker import SupplementTracker

    client = SupplementTracker(api_key="your_api_key")
    supplements = await client.supplements.list()

**JavaScript SDK** (Planned):

.. code-block:: bash

    npm install supplement-tracker-js

.. code-block:: javascript

    import SupplementTracker from 'supplement-tracker-js';

    const client = new SupplementTracker({ apiKey: 'your_api_key' });
    const supplements = await client.supplements.list();

Community SDKs
--------------

Community-maintained libraries will be listed here as they become available.

Examples
========

Complete Example
----------------

Here's a complete example of using the API to track supplements:

.. code-block:: python

    import httpx
    import asyncio

    async def supplement_tracking_example():
        base_url = "https://api.supplementtracker.com/api/v1"
        
        async with httpx.AsyncClient() as client:
            # 1. Register user
            register_data = {
                "email": "<EMAIL>",
                "username": "healthuser",
                "password": "SecurePass123!",
                "full_name": "Health User"
            }
            
            register_response = await client.post(
                f"{base_url}/auth/register",
                json=register_data
            )
            print("User registered:", register_response.status_code)
            
            # 2. Login
            login_data = {
                "username": "healthuser",
                "password": "SecurePass123!"
            }
            
            login_response = await client.post(
                f"{base_url}/auth/login",
                json=login_data
            )
            
            tokens = login_response.json()
            access_token = tokens["access_token"]
            headers = {"Authorization": f"Bearer {access_token}"}
            
            # 3. Create supplement
            supplement_data = {
                "name": "Vitamin D3",
                "category": "Vitamin",
                "form": "Capsule",
                "brand": "Nature's Way"
            }
            
            supplement_response = await client.post(
                f"{base_url}/supplements",
                json=supplement_data,
                headers=headers
            )
            
            supplement = supplement_response.json()["data"]
            print(f"Created supplement: {supplement['name']}")
            
            # 4. Log intake
            intake_data = {
                "supplement_id": supplement["id"],
                "dosage": 5000,
                "dosage_unit": "IU",
                "notes": "Morning dose with breakfast",
                "mood_before": 7,
                "energy_before": 6
            }
            
            intake_response = await client.post(
                f"{base_url}/intakes",
                json=intake_data,
                headers=headers
            )
            
            intake = intake_response.json()["data"]
            print(f"Logged intake: {intake['dosage']} {intake['dosage_unit']}")
            
            # 5. Get intake history
            history_response = await client.get(
                f"{base_url}/intakes/history",
                headers=headers
            )
            
            history = history_response.json()["data"]
            print(f"Total intakes: {len(history)}")

    # Run the example
    asyncio.run(supplement_tracking_example())

Testing the API
===============

**Using curl**:

.. code-block:: bash

    # Test health endpoint
    curl https://api.supplementtracker.com/health
    
    # Test with authentication
    curl -H "Authorization: Bearer YOUR_TOKEN" \
         https://api.supplementtracker.com/api/v1/users/me

**Using Python requests**:

.. code-block:: python

    import requests

    # Test basic endpoint
    response = requests.get("https://api.supplementtracker.com/health")
    print(response.json())

**Using Postman**:

Import the OpenAPI specification from ``https://api.supplementtracker.com/openapi.json`` into Postman for interactive testing.

Support
=======

**Documentation**:
- API Reference: This documentation
- User Guides: :doc:`../user-guide/installation`
- Architecture: :doc:`../architecture/overview`

**Community**:
- GitHub Issues: Bug reports and feature requests
- GitHub Discussions: Questions and community support
- Email: <EMAIL>

**Status**:
- API Status: https://status.supplementtracker.com
- Uptime Monitoring: Real-time API availability

Changelog
=========

**Version 0.1.0** (Current):
- Initial API release
- User authentication and management
- Supplement tracking
- Basic data export

**Planned Updates**:
- Community features (Q3 2025)
- Research tools (Q1 2026)
- Advanced analytics (Q2 2026)

