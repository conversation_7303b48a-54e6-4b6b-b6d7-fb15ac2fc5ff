===============================================
Supplements API
Supplements API
===============================================

This document describes the supplement management endpoints for the Supplement Tracker API.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

The Supplements API provides endpoints for managing supplement data, including creating, reading, updating, and searching supplements.

**Base URL**: ``/api/v1/supplements``

**Authentication**: Required for write operations

Endpoints
=========

List Supplements
----------------
---------------

Get a list of supplements with optional filtering and pagination.

**Endpoint**: ``GET /api/v1/supplements``

**Query Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Type
     - Description
   * - search
     - string
     - Search term for name, brand, or description
   * - category
     - string
     - Filter by supplement category
   * - brand
     - string
     - Filter by brand name
   * - is_verified
     - boolean
     - Filter by verification status
   * - skip
     - integer
     - Number of records to skip (pagination)
   * - limit
     - integer
     - Maximum number of records to return (max 100)

**Response** (200 OK):

.. code-block:: json

    {
        "data": [
            {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "Vitamin D3",
                "brand": "Nature's Way",
                "description": "High-potency vitamin D3 for bone health",
                "category": "Vitamin",
                "form": "Capsule",
                "serving_size": 1,
                "serving_unit": "capsule",
                "ingredients": "Vitamin D3 (as cholecalciferol) 5000 IU",
                "barcode": "123456789012",
                "is_verified": true,
                "created_at": "2025-06-18T15:30:00Z",
                "updated_at": "2025-06-18T15:30:00Z",
                "created_by_user_id": "user_123"
            }
        ],
        "pagination": {
            "total": 150,
            "page": 1,
            "per_page": 20,
            "pages": 8
        }
    }

Get Supplement by ID
--------------------
-------------------

Get detailed information about a specific supplement.

**Endpoint**: ``GET /api/v1/supplements/{supplement_id}``

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "name": "Vitamin D3",
            "brand": "Nature's Way",
            "description": "High-potency vitamin D3 for bone health and immune support",
            "category": "Vitamin",
            "form": "Capsule",
            "serving_size": 1,
            "serving_unit": "capsule",
            "ingredients": "Vitamin D3 (as cholecalciferol) 5000 IU, Rice flour, Gelatin capsule",
            "barcode": "123456789012",
            "is_verified": true,
            "verification_count": 5,
            "created_at": "2025-06-18T15:30:00Z",
            "updated_at": "2025-06-18T15:30:00Z",
            "created_by_user_id": "user_123"
        }
    }

Create Supplement
-----------------
----------------

Create a new supplement entry.

**Endpoint**: ``POST /api/v1/supplements``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body**:

.. code-block:: json

    {
        "name": "Magnesium Glycinate",
        "brand": "Thorne",
        "description": "Highly absorbable form of magnesium",
        "category": "Mineral",
        "form": "Capsule",
        "serving_size": 2,
        "serving_unit": "capsules",
        "ingredients": "Magnesium (as magnesium glycinate) 200mg",
        "barcode": "987654321098"
    }

**Response** (201 Created):

.. code-block:: json

    {
        "data": {
            "id": "456e7890-e89b-12d3-a456-426614174000",
            "name": "Magnesium Glycinate",
            "brand": "Thorne",
            "description": "Highly absorbable form of magnesium",
            "category": "Mineral",
            "form": "Capsule",
            "serving_size": 2,
            "serving_unit": "capsules",
            "ingredients": "Magnesium (as magnesium glycinate) 200mg",
            "barcode": "987654321098",
            "is_verified": false,
            "verification_count": 0,
            "created_at": "2025-06-18T17:30:00Z",
            "updated_at": "2025-06-18T17:30:00Z",
            "created_by_user_id": "user_456"
        }
    }

Update Supplement
-----------------
----------------

Update an existing supplement (requires ownership or admin privileges).

**Endpoint**: ``PUT /api/v1/supplements/{supplement_id}``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body**:

.. code-block:: json

    {
        "description": "Updated description with more detailed information",
        "ingredients": "Complete ingredient list with allergen information"
    }

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "id": "456e7890-e89b-12d3-a456-426614174000",
            "name": "Magnesium Glycinate",
            "brand": "Thorne",
            "description": "Updated description with more detailed information",
            "category": "Mineral",
            "form": "Capsule",
            "serving_size": 2,
            "serving_unit": "capsules",
            "ingredients": "Complete ingredient list with allergen information",
            "barcode": "987654321098",
            "is_verified": false,
            "verification_count": 0,
            "created_at": "2025-06-18T17:30:00Z",
            "updated_at": "2025-06-18T18:00:00Z",
            "created_by_user_id": "user_456"
        }
    }

Search Supplements
------------------
-----------------

Advanced search functionality for supplements.

**Endpoint**: ``GET /api/v1/supplements/search``

**Query Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Type
     - Description
   * - q
     - string
     - Search query (required)
   * - category
     - string
     - Filter by category
   * - verified_only
     - boolean
     - Only return verified supplements
   * - sort
     - string
     - Sort by: "relevance", "name", "created_at"
   * - limit
     - integer
     - Maximum results (max 100)

**Response** (200 OK):

.. code-block:: json

    {
        "data": [
            {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "Vitamin D3",
                "brand": "Nature's Way",
                "category": "Vitamin",
                "is_verified": true,
                "relevance_score": 0.95
            }
        ],
        "search_info": {
            "query": "vitamin d",
            "total_results": 25,
            "search_time_ms": 45
        }
    }

Get Supplement Categories
-------------------------
------------------------

Get list of available supplement categories.

**Endpoint**: ``GET /api/v1/supplements/categories``

**Response** (200 OK):

.. code-block:: json

    {
        "data": [
            {
                "name": "Vitamin",
                "count": 45,
                "description": "Essential vitamins and vitamin complexes"
            },
            {
                "name": "Mineral",
                "count": 32,
                "description": "Essential minerals and trace elements"
            },
            {
                "name": "Amino Acid",
                "count": 18,
                "description": "Individual amino acids and protein supplements"
            },
            {
                "name": "Fatty Acid",
                "count": 12,
                "description": "Omega fatty acids and essential fats"
            },
            {
                "name": "Herb",
                "count": 67,
                "description": "Herbal supplements and botanical extracts"
            },
            {
                "name": "Probiotic",
                "count": 23,
                "description": "Beneficial bacteria and digestive health"
            },
            {
                "name": "Enzyme",
                "count": 15,
                "description": "Digestive and metabolic enzymes"
            },
            {
                "name": "Other",
                "count": 28,
                "description": "Other supplement types"
            }
        ]
    }

Supplement Verification
=======================
======================

Verify Supplement
-----------------
----------------

Mark a supplement as verified (requires appropriate permissions).

**Endpoint**: ``POST /api/v1/supplements/{supplement_id}/verify``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body**:

.. code-block:: json

    {
        "verification_notes": "Verified product information against manufacturer website",
        "confidence_level": "high"
    }

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "supplement_id": "123e4567-e89b-12d3-a456-426614174000",
            "verification_status": "verified",
            "verification_count": 3,
            "verified_by": "user_789",
            "verified_at": "2025-06-18T18:30:00Z",
            "verification_notes": "Verified product information against manufacturer website"
        }
    }

Get Verification History
------------------------
-----------------------

Get verification history for a supplement.

**Endpoint**: ``GET /api/v1/supplements/{supplement_id}/verifications``

**Response** (200 OK):

.. code-block:: json

    {
        "data": [
            {
                "id": "verification_123",
                "verified_by": "user_789",
                "verified_at": "2025-06-18T18:30:00Z",
                "verification_notes": "Verified product information against manufacturer website",
                "confidence_level": "high"
            },
            {
                "id": "verification_456",
                "verified_by": "user_101",
                "verified_at": "2025-06-17T14:20:00Z",
                "verification_notes": "Confirmed ingredients match product label",
                "confidence_level": "medium"
            }
        ],
        "summary": {
            "total_verifications": 2,
            "average_confidence": "high",
            "is_verified": true
        }
    }

Examples
========

Python Example
--------------

.. code-block:: python

    import httpx
    import asyncio

    class SupplementAPI:
        def __init__(self, base_url: str, access_token: str = None):
            self.base_url = base_url
            self.access_token = access_token
            self.client = httpx.AsyncClient()

        @property
        def headers(self):
            headers = {"Content-Type": "application/json"}
            if self.access_token:
                headers["Authorization"] = f"Bearer {self.access_token}"
            return headers

        async def list_supplements(self, **params):
            """List supplements with optional filtering"""
            response = await self.client.get(
                f"{self.base_url}/supplements",
                params=params,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def get_supplement(self, supplement_id: str):
            """Get supplement by ID"""
            response = await self.client.get(
                f"{self.base_url}/supplements/{supplement_id}",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def create_supplement(self, supplement_data: dict):
            """Create a new supplement"""
            response = await self.client.post(
                f"{self.base_url}/supplements",
                json=supplement_data,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def update_supplement(self, supplement_id: str, update_data: dict):
            """Update an existing supplement"""
            response = await self.client.put(
                f"{self.base_url}/supplements/{supplement_id}",
                json=update_data,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def search_supplements(self, query: str, **params):
            """Search supplements"""
            params["q"] = query
            response = await self.client.get(
                f"{self.base_url}/supplements/search",
                params=params,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def get_categories(self):
            """Get supplement categories"""
            response = await self.client.get(
                f"{self.base_url}/supplements/categories",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def verify_supplement(self, supplement_id: str, notes: str = None):
            """Verify a supplement"""
            data = {}
            if notes:
                data["verification_notes"] = notes
                data["confidence_level"] = "high"

            response = await self.client.post(
                f"{self.base_url}/supplements/{supplement_id}/verify",
                json=data,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

    # Usage example
    async def main():
        api = SupplementAPI(
            "https://api.supplementtracker.com/api/v1",
            "your_access_token"
        )

        # Search for vitamin D supplements
        results = await api.search_supplements(
            "vitamin d",
            category="Vitamin",
            verified_only=True
        )
        print(f"Found {len(results['data'])} vitamin D supplements")

        # Get supplement details
        if results["data"]:
            supplement_id = results["data"][0]["id"]
            details = await api.get_supplement(supplement_id)
            print(f"Supplement: {details['data']['name']}")

        # Create a new supplement
        new_supplement = await api.create_supplement({
            "name": "Omega-3 Fish Oil",
            "brand": "Nordic Naturals",
            "category": "Fatty Acid",
            "form": "Softgel",
            "serving_size": 2,
            "serving_unit": "softgels",
            "description": "High-quality fish oil with EPA and DHA"
        })
        print(f"Created supplement: {new_supplement['data']['id']}")

        # Get categories
        categories = await api.get_categories()
        print(f"Available categories: {[cat['name'] for cat in categories['data']]}")

    asyncio.run(main())

JavaScript Example
------------------

.. code-block:: javascript

    class SupplementAPI {
        constructor(baseUrl, accessToken = null) {
            this.baseUrl = baseUrl;
            this.accessToken = accessToken;
        }

        async request(endpoint, options = {}) {
            const url = `${this.baseUrl}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            };

            if (this.accessToken) {
                config.headers['Authorization'] = `Bearer ${this.accessToken}`;
            }

            const response = await fetch(url, config);
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error?.message || 'Request failed');
            }

            return response.json();
        }

        async listSupplements(params = {}) {
            const queryString = new URLSearchParams(params).toString();
            const endpoint = `/supplements${queryString ? '?' + queryString : ''}`;
            return this.request(endpoint);
        }

        async getSupplement(supplementId) {
            return this.request(`/supplements/${supplementId}`);
        }

        async createSupplement(supplementData) {
            return this.request('/supplements', {
                method: 'POST',
                body: JSON.stringify(supplementData)
            });
        }

        async updateSupplement(supplementId, updateData) {
            return this.request(`/supplements/${supplementId}`, {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
        }

        async searchSupplements(query, params = {}) {
            const searchParams = { q: query, ...params };
            const queryString = new URLSearchParams(searchParams).toString();
            return this.request(`/supplements/search?${queryString}`);
        }

        async getCategories() {
            return this.request('/supplements/categories');
        }

        async verifySupplement(supplementId, notes = null) {
            const data = {};
            if (notes) {
                data.verification_notes = notes;
                data.confidence_level = 'high';
            }

            return this.request(`/supplements/${supplementId}/verify`, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        }
    }

    // Usage example
    const api = new SupplementAPI(
        'https://api.supplementtracker.com/api/v1',
        'your_access_token'
    );

    // Search for supplements
    api.searchSupplements('magnesium', { category: 'Mineral' })
        .then(results => {
            console.log('Search results:', results.data);
        })
        .catch(error => {
            console.error('Search failed:', error.message);
        });

    // Create a new supplement
    api.createSupplement({
        name: 'Vitamin C',
        brand: 'NOW Foods',
        category: 'Vitamin',
        form: 'Tablet',
        serving_size: 1,
        serving_unit: 'tablet'
    })
        .then(result => {
            console.log('Created supplement:', result.data);
        });

Related Documentation
=====================
====================

- :doc:`authentication`
- :doc:`users`
- :doc:`schemas`
