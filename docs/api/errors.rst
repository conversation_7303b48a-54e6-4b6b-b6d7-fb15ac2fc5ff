==============
Error Handling
==============

This document describes error handling, status codes, and error response formats for the Supplement Tracker API.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

The Supplement Tracker API uses conventional HTTP status codes and provides detailed error information in a consistent JSON format. All errors include a machine-readable error code and human-readable message.

**Error Response Format**:

.. code-block:: json

    {
        "error": {
            "code": "ERROR_CODE",
            "message": "Human-readable error message",
            "details": {
                "additional": "context-specific information"
            },
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

HTTP Status Codes
=================

Success Codes (2xx)

.. list-table::
   :header-rows: 1
   :widths: 15 85

   * - Code
     - Description
   * - 200
     - OK - Request successful
   * - 201
     - Created - Resource created successfully
   * - 204
     - No Content - Request successful, no response body

Client Error Codes (4xx)

.. list-table::
   :header-rows: 1
   :widths: 15 85

   * - Code
     - Description
   * - 400
     - Bad Request - Invalid request data or parameters
   * - 401
     - Unauthorized - Authentication required or failed
   * - 403
     - Forbidden - Insufficient permissions
   * - 404
     - Not Found - Resource does not exist
   * - 409
     - Conflict - Resource already exists or conflict
   * - 422
     - Unprocessable Entity - Valid request format but semantic errors
   * - 423
     - Locked - Resource is locked (e.g., account locked)
   * - 429
     - Too Many Requests - Rate limit exceeded

Server Error Codes (5xx)

.. list-table::
   :header-rows: 1
   :widths: 15 85

   * - Code
     - Description
   * - 500
     - Internal Server Error - Unexpected server error
   * - 502
     - Bad Gateway - Upstream service error
   * - 503
     - Service Unavailable - Service temporarily unavailable
   * - 504
     - Gateway Timeout - Upstream service timeout

Error Codes
===========

Authentication Errors

**AUTHENTICATION_REQUIRED** (401)

.. code-block:: json

    {
        "error": {
            "code": "AUTHENTICATION_REQUIRED",
            "message": "Authentication credentials are required"
        }
    }

**AUTHENTICATION_FAILED** (401)

.. code-block:: json

    {
        "error": {
            "code": "AUTHENTICATION_FAILED",
            "message": "Invalid username or password"
        }
    }

**TOKEN_EXPIRED** (401)

.. code-block:: json

    {
        "error": {
            "code": "TOKEN_EXPIRED",
            "message": "Access token has expired",
            "details": {
                "expired_at": "2025-06-18T15:30:00Z"
            }
        }
    }

**INVALID_TOKEN** (401)

.. code-block:: json

    {
        "error": {
            "code": "INVALID_TOKEN",
            "message": "Token format is invalid or corrupted"
        }
    }

**ACCOUNT_LOCKED** (423)

.. code-block:: json

    {
        "error": {
            "code": "ACCOUNT_LOCKED",
            "message": "Account temporarily locked due to multiple failed login attempts",
            "details": {
                "lockout_expires_at": "2025-06-18T16:00:00Z",
                "retry_after": 300
            }
        }
    }

Authorization Errors

**AUTHORIZATION_FAILED** (403)

.. code-block:: json

    {
        "error": {
            "code": "AUTHORIZATION_FAILED",
            "message": "Insufficient permissions to access this resource",
            "details": {
                "required_permission": "write:supplements",
                "user_permissions": ["read:supplements", "read:own_data"]
            }
        }
    }

**RESOURCE_OWNERSHIP_REQUIRED** (403)

.. code-block:: json

    {
        "error": {
            "code": "RESOURCE_OWNERSHIP_REQUIRED",
            "message": "You can only access your own resources"
        }
    }

Validation Errors

**VALIDATION_ERROR** (400)

.. code-block:: json

    {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Invalid input data",
            "details": {
                "field": "email",
                "issue": "Invalid email format",
                "provided_value": "invalid-email"
            }
        }
    }

**MISSING_REQUIRED_FIELD** (400)

.. code-block:: json

    {
        "error": {
            "code": "MISSING_REQUIRED_FIELD",
            "message": "Required field is missing",
            "details": {
                "field": "password",
                "required_fields": ["email", "username", "password"]
            }
        }
    }

**INVALID_FIELD_VALUE** (400)

.. code-block:: json

    {
        "error": {
            "code": "INVALID_FIELD_VALUE",
            "message": "Field value is invalid",
            "details": {
                "field": "mood_before",
                "issue": "Value must be between 1 and 10",
                "provided_value": 15,
                "valid_range": [1, 10]
            }
        }
    }

**INVALID_UUID_FORMAT** (400)

.. code-block:: json

    {
        "error": {
            "code": "INVALID_UUID_FORMAT",
            "message": "Invalid UUID format",
            "details": {
                "field": "supplement_id",
                "provided_value": "invalid-uuid",
                "expected_format": "UUID v4 (e.g., 123e4567-e89b-12d3-a456-************)"
            }
        }
    }

Resource Errors

**RESOURCE_NOT_FOUND** (404)

.. code-block:: json

    {
        "error": {
            "code": "RESOURCE_NOT_FOUND",
            "message": "Requested resource does not exist",
            "details": {
                "resource_type": "supplement",
                "resource_id": "123e4567-e89b-12d3-a456-************"
            }
        }
    }

**RESOURCE_CONFLICT** (409)

.. code-block:: json

    {
        "error": {
            "code": "RESOURCE_CONFLICT",
            "message": "Resource already exists",
            "details": {
                "field": "email",
                "value": "<EMAIL>",
                "conflict_type": "unique_constraint"
            }
        }
    }

**RESOURCE_DELETED** (410)

.. code-block:: json

    {
        "error": {
            "code": "RESOURCE_DELETED",
            "message": "Resource has been deleted",
            "details": {
                "resource_type": "user",
                "deleted_at": "2025-06-18T14:00:00Z"
            }
        }
    }

Rate Limiting Errors

**RATE_LIMIT_EXCEEDED** (429)

.. code-block:: json

    {
        "error": {
            "code": "RATE_LIMIT_EXCEEDED",
            "message": "Too many requests",
            "details": {
                "limit": 1000,
                "window": "1 hour",
                "retry_after": 3600,
                "reset_at": "2025-06-18T16:00:00Z"
            }
        }
    }

**QUOTA_EXCEEDED** (429)

.. code-block:: json

    {
        "error": {
            "code": "QUOTA_EXCEEDED",
            "message": "Monthly API quota exceeded",
            "details": {
                "quota_limit": 10000,
                "quota_used": 10000,
                "quota_resets_at": "2025-07-01T00:00:00Z"
            }
        }
    }

Server Errors

**INTERNAL_SERVER_ERROR** (500)

.. code-block:: json

    {
        "error": {
            "code": "INTERNAL_SERVER_ERROR",
            "message": "An unexpected error occurred",
            "details": {
                "incident_id": "inc_123456789"
            }
        }
    }

**DATABASE_ERROR** (500)

.. code-block:: json

    {
        "error": {
            "code": "DATABASE_ERROR",
            "message": "Database operation failed",
            "details": {
                "operation": "insert",
                "incident_id": "inc_123456789"
            }
        }
    }

**SERVICE_UNAVAILABLE** (503)

.. code-block:: json

    {
        "error": {
            "code": "SERVICE_UNAVAILABLE",
            "message": "Service is temporarily unavailable",
            "details": {
                "maintenance_window": "2025-06-18T02:00:00Z to 2025-06-18T04:00:00Z",
                "retry_after": 7200
            }
        }
    }

Error Handling Examples
=======================
======================

Python Error Handling

.. code-block:: python

    import httpx
    import asyncio
    from typing import Optional

    class APIError(Exception):
        def __init__(self, error_data: dict, status_code: int):
            self.code = error_data.get('code')
            self.message = error_data.get('message')
            self.details = error_data.get('details', {})
            self.status_code = status_code
            self.timestamp = error_data.get('timestamp')
            self.request_id = error_data.get('request_id')
            super().__init__(self.message)

    class SupplementTrackerClient:
        def __init__(self, base_url: str, access_token: Optional[str] = None):
            self.base_url = base_url
            self.access_token = access_token
            self.client = httpx.AsyncClient()

        async def request(self, method: str, endpoint: str, **kwargs):
            """Make API request with comprehensive error handling"""
            headers = kwargs.get('headers', {})
            if self.access_token:
                headers['Authorization'] = f'Bearer {self.access_token}'
            kwargs['headers'] = headers

            try:
                response = await self.client.request(
                    method, f"{self.base_url}{endpoint}", **kwargs
                )
                
                # Handle successful responses
                if response.status_code < 400:
                    return response.json() if response.content else None
                
                # Handle error responses
                try:
                    error_data = response.json().get('error', {})
                except:
                    error_data = {
                        'code': 'HTTP_ERROR',
                        'message': f'HTTP {response.status_code}: {response.text}'
                    }
                
                raise APIError(error_data, response.status_code)
                
            except httpx.RequestError as e:
                raise APIError({
                    'code': 'NETWORK_ERROR',
                    'message': f'Network error: {str(e)}'
                }, 0)

        async def handle_api_error(self, error: APIError):
            """Handle specific API errors"""
            if error.code == 'TOKEN_EXPIRED':
                # Try to refresh token
                await self.refresh_token()
                return True  # Retry the request
            
            elif error.code == 'RATE_LIMIT_EXCEEDED':
                # Wait and retry
                retry_after = error.details.get('retry_after', 60)
                print(f"Rate limited, waiting {retry_after} seconds...")
                await asyncio.sleep(retry_after)
                return True  # Retry the request
            
            elif error.code == 'ACCOUNT_LOCKED':
                # Handle account lockout
                lockout_expires = error.details.get('lockout_expires_at')
                print(f"Account locked until {lockout_expires}")
                return False  # Don't retry
            
            elif error.code == 'VALIDATION_ERROR':
                # Handle validation errors
                field = error.details.get('field')
                issue = error.details.get('issue')
                print(f"Validation error in field '{field}': {issue}")
                return False  # Don't retry
            
            else:
                # Log unexpected errors
                print(f"API Error: {error.code} - {error.message}")
                return False

        async def request_with_retry(self, method: str, endpoint: str, 
                                   max_retries: int = 3, **kwargs):
            """Make API request with automatic retry logic"""
            for attempt in range(max_retries + 1):
                try:
                    return await self.request(method, endpoint, **kwargs)
                
                except APIError as e:
                    if attempt < max_retries:
                        should_retry = await self.handle_api_error(e)
                        if should_retry:
                            continue
                    raise

    # Usage example
    async def main():
        client = SupplementTrackerClient(
            "https://api.supplementtracker.com/api/v1",
            "your_access_token"
        )

        try:
            # This will automatically handle token refresh, rate limiting, etc.
            supplements = await client.request_with_retry(
                "GET", "/supplements", params={"search": "vitamin"}
            )
            print(f"Found {len(supplements['data'])} supplements")
            
        except APIError as e:
            print(f"API Error: {e.code} - {e.message}")
            if e.details:
                print(f"Details: {e.details}")

    asyncio.run(main())

JavaScript Error Handling

.. code-block:: javascript

    class APIError extends Error {
        constructor(errorData, statusCode) {
            super(errorData.message || 'API Error');
            this.name = 'APIError';
            this.code = errorData.code;
            this.details = errorData.details || {};
            this.statusCode = statusCode;
            this.timestamp = errorData.timestamp;
            this.requestId = errorData.request_id;
        }
    }

    class SupplementTrackerClient {
        constructor(baseUrl, accessToken = null) {
            this.baseUrl = baseUrl;
            this.accessToken = accessToken;
        }

        async request(method, endpoint, options = {}) {
            const url = `${this.baseUrl}${endpoint}`;
            const config = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            };

            if (this.accessToken) {
                config.headers['Authorization'] = `Bearer ${this.accessToken}`;
            }

            try {
                const response = await fetch(url, config);
                
                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return await response.json();
                    }
                    return null;
                }

                // Handle error responses
                let errorData;
                try {
                    const errorResponse = await response.json();
                    errorData = errorResponse.error || {};
                } catch {
                    errorData = {
                        code: 'HTTP_ERROR',
                        message: `HTTP ${response.status}: ${response.statusText}`
                    };
                }

                throw new APIError(errorData, response.status);

            } catch (error) {
                if (error instanceof APIError) {
                    throw error;
                }
                
                // Network or other errors
                throw new APIError({
                    code: 'NETWORK_ERROR',
                    message: `Network error: ${error.message}`
                }, 0);
            }
        }

        async handleAPIError(error) {
            switch (error.code) {
                case 'TOKEN_EXPIRED':
                    // Try to refresh token
                    await this.refreshToken();
                    return true; // Retry

                case 'RATE_LIMIT_EXCEEDED':
                    // Wait and retry
                    const retryAfter = error.details.retry_after || 60;
                    console.log(`Rate limited, waiting ${retryAfter} seconds...`);
                    await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
                    return true; // Retry

                case 'ACCOUNT_LOCKED':
                    // Handle account lockout
                    const lockoutExpires = error.details.lockout_expires_at;
                    console.log(`Account locked until ${lockoutExpires}`);
                    return false; // Don't retry

                case 'VALIDATION_ERROR':
                    // Handle validation errors
                    const field = error.details.field;
                    const issue = error.details.issue;
                    console.error(`Validation error in field '${field}': ${issue}`);
                    return false; // Don't retry

                default:
                    console.error(`API Error: ${error.code} - ${error.message}`);
                    return false;
            }
        }

        async requestWithRetry(method, endpoint, maxRetries = 3, options = {}) {
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    return await this.request(method, endpoint, options);
                } catch (error) {
                    if (error instanceof APIError && attempt < maxRetries) {
                        const shouldRetry = await this.handleAPIError(error);
                        if (shouldRetry) {
                            continue;
                        }
                    }
                    throw error;
                }
            }
        }

        async refreshToken() {
            // Implementation for token refresh
            const refreshToken = localStorage.getItem('refresh_token');
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }

            try {
                const response = await this.request('POST', '/auth/refresh', {
                    body: JSON.stringify({ refresh_token: refreshToken })
                });

                this.accessToken = response.access_token;
                localStorage.setItem('access_token', response.access_token);
            } catch (error) {
                // Refresh failed, redirect to login
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                window.location.href = '/login';
                throw error;
            }
        }
    }

    // Usage example
    const client = new SupplementTrackerClient(
        'https://api.supplementtracker.com/api/v1',
        localStorage.getItem('access_token')
    );

    // Example with comprehensive error handling
    async function getSupplements() {
        try {
            const supplements = await client.requestWithRetry(
                'GET', 
                '/supplements',
                3, // max retries
                { 
                    headers: { 'Accept': 'application/json' }
                }
            );
            
            console.log('Supplements:', supplements.data);
            return supplements;
            
        } catch (error) {
            if (error instanceof APIError) {
                // Handle specific API errors
                switch (error.code) {
                    case 'AUTHENTICATION_REQUIRED':
                        window.location.href = '/login';
                        break;
                    case 'AUTHORIZATION_FAILED':
                        alert('You do not have permission to access this resource');
                        break;
                    case 'NETWORK_ERROR':
                        alert('Network error. Please check your connection.');
                        break;
                    default:
                        alert(`Error: ${error.message}`);
                }
            } else {
                console.error('Unexpected error:', error);
                alert('An unexpected error occurred');
            }
        }
    }

Best Practices
==============

Error Handling Guidelines

1. **Always check status codes**: Don't assume requests succeeded
2. **Parse error responses**: Extract meaningful error information
3. **Implement retry logic**: Handle transient errors gracefully
4. **Log errors appropriately**: Include request IDs for debugging
5. **Provide user feedback**: Show meaningful error messages
6. **Handle authentication**: Implement token refresh logic
7. **Respect rate limits**: Implement exponential backoff

Common Error Scenarios

**Token Expiration**:
- Implement automatic token refresh
- Fallback to login if refresh fails
- Store tokens securely

**Rate Limiting**:
- Respect retry-after headers
- Implement exponential backoff
- Consider request queuing

**Network Issues**:
- Implement connection timeouts
- Retry with exponential backoff
- Provide offline functionality where possible

**Validation Errors**:
- Validate input client-side when possible
- Provide real-time validation feedback
- Handle server-side validation gracefully

Debugging Tips
==============
=============

Request IDs

Every error response includes a unique request ID that can be used for debugging:

.. code-block:: json

    {
        "error": {
            "code": "INTERNAL_SERVER_ERROR",
            "message": "An unexpected error occurred",
            "request_id": "req_123456789"
        }
    }

Include this request ID when contacting support for faster resolution.

Error Logging

Log errors with sufficient context for debugging:

.. code-block:: python

    import logging

    logger = logging.getLogger(__name__)

    try:
        response = await client.request("GET", "/supplements")
    except APIError as e:
        logger.error(
            "API request failed",
            extra={
                "error_code": e.code,
                "error_message": e.message,
                "status_code": e.status_code,
                "request_id": e.request_id,
                "endpoint": "/supplements",
                "method": "GET"
            }
        )

Related Documentation
=====================
====================

- :doc:`authentication`
- :doc:`users`
- :doc:`supplements`
- :doc:`schemas`
