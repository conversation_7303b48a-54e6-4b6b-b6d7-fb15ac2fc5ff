==================
Authentication API
==================

This document describes the authentication endpoints for the Supplement Tracker API.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

The Authentication API provides endpoints for user registration, login, logout, and token management. All authentication uses JWT (JSON Web Tokens) for secure, stateless authentication.

**Base URL**: ``/api/v1/auth``

**Authentication Flow**:

1. Register a new user account
2. Login to receive access and refresh tokens
3. Use access token for authenticated requests
4. Refresh access token when it expires
5. Logout to invalidate tokens

Endpoints
=========

Register User

Create a new user account.

**Endpoint**: ``POST /api/v1/auth/register``

**Request Body**:

.. code-block:: json

    {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "SecurePass123!",
        "full_name": "Test User",
        "bio": "Health enthusiast"
    }

**Response** (201 Created):

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-************",
            "email": "<EMAIL>",
            "username": "testuser",
            "full_name": "Test User",
            "bio": "Health enthusiast",
            "is_active": true,
            "is_verified": false,
            "created_at": "2025-06-18T15:30:00Z",
            "updated_at": "2025-06-18T15:30:00Z",
            "last_login_at": null
        }
    }

**Error Responses**:

- ``400 Bad Request``: Validation errors
- ``409 Conflict``: Email or username already exists

User Login

Authenticate user and receive tokens.

**Endpoint**: ``POST /api/v1/auth/login``

**Request Body**:

.. code-block:: json

    {
        "username": "testuser",
        "password": "SecurePass123!"
    }

**Alternative with email**:

.. code-block:: json

    {
        "email": "<EMAIL>",
        "password": "SecurePass123!"
    }

**Response** (200 OK):

.. code-block:: json

    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer",
        "expires_in": 900,
        "user": {
            "id": "123e4567-e89b-12d3-a456-************",
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "is_verified": false
        }
    }

**Error Responses**:

- ``401 Unauthorized``: Invalid credentials
- ``423 Locked``: Account temporarily locked

Refresh Token

Get a new access token using refresh token.

**Endpoint**: ``POST /api/v1/auth/refresh``

**Request Body**:

.. code-block:: json

    {
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }

**Response** (200 OK):

.. code-block:: json

    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer",
        "expires_in": 900
    }

**Error Responses**:

- ``401 Unauthorized``: Invalid or expired refresh token

User Logout

Invalidate user tokens and end session.

**Endpoint**: ``POST /api/v1/auth/logout``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body** (Optional):

.. code-block:: json

    {
        "revoke_all_sessions": false
    }

**Response** (200 OK):

.. code-block:: json

    {
        "message": "Successfully logged out"
    }

Password Reset Request

Request a password reset email.

**Endpoint**: ``POST /api/v1/auth/password-reset-request``

**Request Body**:

.. code-block:: json

    {
        "email": "<EMAIL>"
    }

**Response** (200 OK):

.. code-block:: json

    {
        "message": "If an account with this email exists, a password reset link has been sent."
    }

Password Reset

Reset password using token from email.

**Endpoint**: ``POST /api/v1/auth/password-reset``

**Request Body**:

.. code-block:: json

    {
        "token": "password_reset_token_from_email",
        "new_password": "NewSecurePass123!"
    }

**Response** (200 OK):

.. code-block:: json

    {
        "message": "Password reset successfully"
    }

Change Password

Change password for authenticated user.

**Endpoint**: ``PUT /api/v1/auth/password``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body**:

.. code-block:: json

    {
        "current_password": "SecurePass123!",
        "new_password": "NewSecurePass456!"
    }

**Response** (200 OK):

.. code-block:: json

    {
        "message": "Password updated successfully"
    }

Get User Permissions

Get current user's permissions and roles.

**Endpoint**: ``GET /api/v1/auth/permissions``

**Headers**: ``Authorization: Bearer {access_token}``

**Response** (200 OK):

.. code-block:: json

    {
        "data": {
            "user_id": "123e4567-e89b-12d3-a456-************",
            "permissions": [
                "read:own_data",
                "write:own_data",
                "read:supplements",
                "write:supplements"
            ],
            "roles": ["user"]
        }
    }

API Key Management
==================

Create API Key

Create a new API key for programmatic access.

**Endpoint**: ``POST /api/v1/auth/api-keys``

**Headers**: ``Authorization: Bearer {access_token}``

**Request Body**:

.. code-block:: json

    {
        "name": "Data Analysis Script",
        "description": "API key for automated data analysis",
        "permissions": ["read:supplements", "read:own_intakes"],
        "expires_at": "2025-12-31T23:59:59Z"
    }

**Response** (201 Created):

.. code-block:: json

    {
        "data": {
            "id": "key_123456789",
            "name": "Data Analysis Script",
            "key": "sk_live_abcdef123456789...",
            "permissions": ["read:supplements", "read:own_intakes"],
            "created_at": "2025-06-18T15:30:00Z",
            "expires_at": "2025-12-31T23:59:59Z",
            "last_used_at": null
        },
        "warning": "This API key will only be shown once. Please store it securely."
    }

List API Keys

Get list of user's API keys.

**Endpoint**: ``GET /api/v1/auth/api-keys``

**Headers**: ``Authorization: Bearer {access_token}``

**Response** (200 OK):

.. code-block:: json

    {
        "data": [
            {
                "id": "key_123456789",
                "name": "Data Analysis Script",
                "permissions": ["read:supplements", "read:own_intakes"],
                "created_at": "2025-06-18T15:30:00Z",
                "expires_at": "2025-12-31T23:59:59Z",
                "last_used_at": "2025-06-18T16:00:00Z"
            }
        ]
    }

Revoke API Key

Revoke an API key.

**Endpoint**: ``DELETE /api/v1/auth/api-keys/{key_id}``

**Headers**: ``Authorization: Bearer {access_token}``

**Response** (200 OK):

.. code-block:: json

    {
        "message": "API key revoked successfully"
    }

Authentication Examples
=======================

Python Example

.. code-block:: python

    import httpx
    import asyncio
    from datetime import datetime

    class AuthClient:
        def __init__(self, base_url: str):
            self.base_url = base_url
            self.access_token = None
            self.refresh_token = None
            self.client = httpx.AsyncClient()

        async def register(self, email: str, username: str, password: str, **kwargs):
            """Register a new user"""
            data = {
                "email": email,
                "username": username,
                "password": password,
                **kwargs
            }
            
            response = await self.client.post(
                f"{self.base_url}/auth/register",
                json=data
            )
            response.raise_for_status()
            return response.json()

        async def login(self, username: str, password: str):
            """Login and store tokens"""
            data = {"username": username, "password": password}
            
            response = await self.client.post(
                f"{self.base_url}/auth/login",
                json=data
            )
            response.raise_for_status()
            
            result = response.json()
            self.access_token = result["access_token"]
            self.refresh_token = result["refresh_token"]
            
            return result

        async def refresh_access_token(self):
            """Refresh the access token"""
            if not self.refresh_token:
                raise ValueError("No refresh token available")
            
            data = {"refresh_token": self.refresh_token}
            
            response = await self.client.post(
                f"{self.base_url}/auth/refresh",
                json=data
            )
            response.raise_for_status()
            
            result = response.json()
            self.access_token = result["access_token"]
            
            return result

        async def logout(self):
            """Logout and clear tokens"""
            if self.access_token:
                headers = {"Authorization": f"Bearer {self.access_token}"}
                await self.client.post(
                    f"{self.base_url}/auth/logout",
                    headers=headers
                )
            
            self.access_token = None
            self.refresh_token = None

        @property
        def auth_headers(self):
            """Get authorization headers"""
            if not self.access_token:
                raise ValueError("Not authenticated")
            return {"Authorization": f"Bearer {self.access_token}"}

    # Usage example
    async def main():
        client = AuthClient("https://api.supplementtracker.com/api/v1")
        
        # Register new user
        await client.register(
            email="<EMAIL>",
            username="testuser",
            password="SecurePass123!",
            full_name="Test User"
        )
        
        # Login
        login_result = await client.login("testuser", "SecurePass123!")
        print(f"Logged in as: {login_result['user']['username']}")
        
        # Use authenticated endpoints
        response = await client.client.get(
            f"{client.base_url}/users/me",
            headers=client.auth_headers
        )
        user_data = response.json()
        print(f"User data: {user_data}")
        
        # Logout
        await client.logout()

    asyncio.run(main())

JavaScript Example

.. code-block:: javascript

    class AuthManager {
        constructor(baseUrl) {
            this.baseUrl = baseUrl;
            this.accessToken = localStorage.getItem('access_token');
            this.refreshToken = localStorage.getItem('refresh_token');
        }

        async register(userData) {
            const response = await fetch(`${this.baseUrl}/auth/register`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error?.message || 'Registration failed');
            }

            return response.json();
        }

        async login(username, password) {
            const response = await fetch(`${this.baseUrl}/auth/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error?.message || 'Login failed');
            }

            const data = await response.json();
            this.setTokens(data.access_token, data.refresh_token);
            return data;
        }

        async refreshAccessToken() {
            if (!this.refreshToken) {
                throw new Error('No refresh token available');
            }

            const response = await fetch(`${this.baseUrl}/auth/refresh`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ refresh_token: this.refreshToken })
            });

            if (!response.ok) {
                this.clearTokens();
                throw new Error('Token refresh failed');
            }

            const data = await response.json();
            this.setTokens(data.access_token, this.refreshToken);
            return data;
        }

        async logout() {
            if (this.accessToken) {
                try {
                    await fetch(`${this.baseUrl}/auth/logout`, {
                        method: 'POST',
                        headers: { 'Authorization': `Bearer ${this.accessToken}` }
                    });
                } catch (error) {
                    console.error('Logout request failed:', error);
                }
            }
            this.clearTokens();
        }

        setTokens(accessToken, refreshToken) {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            localStorage.setItem('access_token', accessToken);
            localStorage.setItem('refresh_token', refreshToken);
        }

        clearTokens() {
            this.accessToken = null;
            this.refreshToken = null;
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
        }

        getAuthHeaders() {
            if (!this.accessToken) {
                throw new Error('Not authenticated');
            }
            return { 'Authorization': `Bearer ${this.accessToken}` };
        }

        async authenticatedRequest(url, options = {}) {
            let response = await fetch(url, {
                ...options,
                headers: {
                    ...this.getAuthHeaders(),
                    ...options.headers
                }
            });

            // If token expired, try to refresh and retry
            if (response.status === 401) {
                await this.refreshAccessToken();
                response = await fetch(url, {
                    ...options,
                    headers: {
                        ...this.getAuthHeaders(),
                        ...options.headers
                    }
                });
            }

            return response;
        }
    }

    // Usage example
    const auth = new AuthManager('https://api.supplementtracker.com/api/v1');

    // Register
    try {
        await auth.register({
            email: '<EMAIL>',
            username: 'testuser',
            password: 'SecurePass123!',
            full_name: 'Test User'
        });
        console.log('Registration successful');
    } catch (error) {
        console.error('Registration failed:', error.message);
    }

    // Login
    try {
        const loginResult = await auth.login('testuser', 'SecurePass123!');
        console.log('Login successful:', loginResult.user);
    } catch (error) {
        console.error('Login failed:', error.message);
    }

    // Make authenticated request
    try {
        const response = await auth.authenticatedRequest('/api/v1/users/me');
        const userData = await response.json();
        console.log('User data:', userData);
    } catch (error) {
        console.error('Request failed:', error.message);
    }

Error Handling
==============

Common Error Responses

**Validation Error** (400 Bad Request):

.. code-block:: json

    {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Invalid input data",
            "details": {
                "field": "password",
                "issue": "Password must be at least 8 characters"
            }
        }
    }

**Authentication Failed** (401 Unauthorized):

.. code-block:: json

    {
        "error": {
            "code": "AUTHENTICATION_FAILED",
            "message": "Invalid username or password"
        }
    }

**Account Locked** (423 Locked):

.. code-block:: json

    {
        "error": {
            "code": "ACCOUNT_LOCKED",
            "message": "Account temporarily locked due to multiple failed login attempts",
            "details": {
                "lockout_expires_at": "2025-06-18T16:00:00Z",
                "retry_after": 300
            }
        }
    }

**Resource Conflict** (409 Conflict):

.. code-block:: json

    {
        "error": {
            "code": "RESOURCE_CONFLICT",
            "message": "Email already registered",
            "details": {
                "field": "email",
                "value": "<EMAIL>"
            }
        }
    }

Security Considerations
=======================
======================

Token Security

1. **Store tokens securely**: Use secure storage mechanisms
2. **Use HTTPS only**: Never send tokens over HTTP
3. **Implement token refresh**: Handle token expiration gracefully
4. **Clear tokens on logout**: Remove tokens from storage

Rate Limiting

Authentication endpoints have strict rate limits:

- **Login**: 5 attempts per minute per IP
- **Registration**: 3 attempts per minute per IP
- **Password reset**: 1 attempt per minute per email

Best Practices
==============
=============

1. **Implement automatic token refresh**
2. **Handle authentication errors gracefully**
3. **Use secure password requirements**
4. **Implement proper logout functionality**
5. **Monitor for suspicious authentication activity**
6. **Use API keys for server-to-server communication**
7. **Implement proper session management**

Related Documentation
=====================
====================

- :doc:`../user-guide/authentication`
- :doc:`users`
- :doc:`errors`
