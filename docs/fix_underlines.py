#!/usr/bin/env python3
"""
Script to fix RST title underline issues in documentation files.
"""

import os
import re
import sys
from pathlib import Path


def fix_underlines_in_file(file_path):
    """Fix underline issues in a single RST file."""
    print(f"Checking {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed = False
    new_lines = []
    
    for i, line in enumerate(lines):
        new_lines.append(line)
        
        # Check if this line is a title (next line is underline)
        if i + 1 < len(lines):
            next_line = lines[i + 1]
            
            # Check for underline characters
            if re.match(r'^[-=~^"\'`#*+<>_]+\s*$', next_line):
                title_length = len(line.rstrip())
                underline_length = len(next_line.rstrip())
                underline_char = next_line.strip()[0] if next_line.strip() else '-'
                
                # If underline is too short, fix it
                if underline_length < title_length:
                    print(f"  Fixing underline at line {i+2}: '{line.strip()}'")
                    print(f"    Title length: {title_length}, Underline length: {underline_length}")
                    
                    # Create new underline with correct length
                    new_underline = underline_char * title_length + '\n'
                    new_lines.append(new_underline)
                    fixed = True
                    
                    # Skip the original underline
                    continue
        
        # Add the next line if it's not an underline we're fixing
        if i + 1 < len(lines) and not (i + 1 < len(new_lines)):
            new_lines.append(lines[i + 1])
    
    # Remove the last line if we added it twice
    if len(new_lines) > len(lines):
        new_lines = new_lines[:-1]
    
    if fixed:
        print(f"  Fixed underlines in {file_path}")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        return True
    
    return False


def main():
    """Main function to fix underlines in all RST files."""
    docs_dir = Path(__file__).parent
    rst_files = list(docs_dir.rglob('*.rst'))
    
    print(f"Found {len(rst_files)} RST files to check...")
    
    total_fixed = 0
    for rst_file in rst_files:
        if fix_underlines_in_file(rst_file):
            total_fixed += 1
    
    print(f"\nFixed underlines in {total_fixed} files.")
    
    if total_fixed > 0:
        print("Please review the changes and commit them if they look correct.")
    else:
        print("No underline issues found.")


if __name__ == '__main__':
    main()
