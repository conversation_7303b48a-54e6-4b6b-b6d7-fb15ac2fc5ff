# Supplement Tracker Documentation

This directory contains the complete documentation for the Supplement Tracker project, built with [Sphinx](https://www.sphinx-doc.org/).

## Quick Start

### Building the Documentation

**Using the build script (recommended):**
```bash
./build.sh html
```

**Using Nix:**
```bash
nix-shell -p python311Packages.sphinx --run "sphinx-build -b html . _build/html"
```

**Using Make:**
```bash
make html
```

**Using system Sphinx:**
```bash
pip install -r requirements.txt
sphinx-build -b html . _build/html
```

### Opening the Documentation

After building, open `_build/html/index.html` in your browser, or use:
```bash
make open
```

## Documentation Structure

```
docs/
├── index.rst                    # Main documentation index
├── user-guide/                  # User-facing documentation
│   ├── installation.rst         # Installation and setup
│   ├── configuration.rst        # Configuration guide
│   ├── authentication.rst       # Authentication guide
│   ├── api-usage.rst            # API usage examples
│   ├── supplement-tracking.rst  # Supplement tracking guide
│   ├── community-features.rst   # Community features (planned)
│   └── troubleshooting.rst      # Troubleshooting guide
├── api/                         # API reference documentation
│   ├── index.rst               # API overview
│   ├── authentication.rst      # Authentication endpoints
│   ├── users.rst               # User management endpoints
│   ├── supplements.rst         # Supplement endpoints
│   ├── community.rst           # Community endpoints (planned)
│   ├── research.rst            # Research endpoints (planned)
│   ├── schemas.rst             # Data schemas and validation
│   └── errors.rst              # Error handling and codes
├── architecture/               # Technical architecture
│   ├── overview.rst            # System overview
│   ├── database-design.rst     # Database schema and design
│   ├── api-design.rst          # API design principles
│   ├── security.rst            # Security architecture
│   ├── deployment.rst          # Deployment strategies
│   ├── monitoring.rst          # Monitoring and observability
│   └── system-design.rst       # Detailed system design
└── developer/                  # Developer guides
    ├── setup.rst               # Development environment setup
    └── contributing.rst        # Contributing guidelines
```

## Build Options

### Standard Builds

- `html` - HTML documentation (default)
- `singlehtml` - Single-page HTML
- `latex` - LaTeX output
- `pdf` - PDF output (requires LaTeX)
- `epub` - EPUB e-book format

### Development Builds

- `make dev` - Build and open in browser
- `make strict` - Build with warnings as errors
- `make linkcheck` - Check for broken links
- `make clean` - Clean build directory

### Using the Build Script

```bash
# Standard build
./build.sh html

# Strict build (warnings as errors)
./build.sh html true

# Other formats
./build.sh latex
./build.sh epub
```

## Development Workflow

1. **Edit documentation files** (`.rst` files)
2. **Build locally** to test changes:
   ```bash
   ./build.sh html
   ```
3. **Check for warnings** and fix them:
   ```bash
   ./build.sh html true
   ```
4. **Check links** (optional):
   ```bash
   make linkcheck
   ```
5. **Commit changes** when satisfied

## Writing Guidelines

### reStructuredText (RST) Syntax

- Use `.rst` files for all documentation
- Follow [reStructuredText syntax](https://www.sphinx-doc.org/en/master/usage/restructuredtext/basics.html)
- Use consistent heading levels:
  ```rst
  ===============================================
  Document Title (H1)
  ===============================================
  
  Section Title (H2)
  ==================
  
  Subsection Title (H3)
  ---------------------
  
  Sub-subsection Title (H4)
  ~~~~~~~~~~~~~~~~~~~~~~~~~
  ```

### Code Examples

Use appropriate syntax highlighting:

```rst
.. code-block:: python

    def example_function():
        return "Hello, World!"

.. code-block:: bash

    curl -X GET "https://api.example.com/endpoint"

.. code-block:: json

    {
        "key": "value"
    }
```

### Cross-References

Link to other documents:
```rst
:doc:`installation`
:doc:`../api/authentication`
:doc:`/user-guide/configuration`
```

### Tables

Use list-table for complex tables:
```rst
.. list-table::
   :header-rows: 1
   :widths: 20 80

   * - Parameter
     - Description
   * - name
     - The name of the item
```

### Admonitions

Use admonitions for important information:
```rst
.. note::
   This is a note.

.. warning::
   This is a warning.

.. important::
   This is important information.
```

## Configuration

The documentation is configured in `conf.py`. Key settings:

- **Project information**: Name, version, author
- **Extensions**: Sphinx extensions used
- **Theme**: HTML theme and styling
- **Intersphinx**: Links to external documentation

## Troubleshooting

### Common Issues

**Build fails with "command not found":**
- Install Sphinx: `pip install sphinx`
- Or use Nix: `nix-shell -p python311Packages.sphinx`

**Warnings about missing documents:**
- Check that all referenced documents exist
- Verify file paths in `:doc:` references

**CSS not loading:**
- Ensure `_static/custom.css` exists
- Check `html_css_files` in `conf.py`

**Links not working:**
- Run `make linkcheck` to find broken links
- Update or remove broken references

### Getting Help

- [Sphinx Documentation](https://www.sphinx-doc.org/)
- [reStructuredText Guide](https://docutils.sourceforge.io/rst.html)
- [Project Issues](https://github.com/forkrul/day2-supplement-tracker/issues)

## Contributing

See :doc:`developer/contributing` for guidelines on contributing to the documentation.

Key points:
- Test builds locally before submitting
- Follow the writing guidelines
- Update relevant sections when adding features
- Include code examples where helpful
- Check for broken links and references
