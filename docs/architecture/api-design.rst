==========
API Design
==========

This document describes the REST API design principles, structure, and conventions used in the Supplement Tracker platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

API Design Principles
=====================

RESTful Architecture

The API follows REST (Representational State Transfer) principles:

- **Resource-based URLs**: Each endpoint represents a resource
- **HTTP methods**: GET, POST, PUT, DELETE for different operations
- **Stateless**: Each request contains all necessary information
- **Cacheable**: Responses include appropriate caching headers
- **Uniform interface**: Consistent patterns across all endpoints

OpenAPI Specification

The API is fully documented using OpenAPI 3.0 specification:

- **Automatic documentation** generation with FastAPI
- **Interactive API explorer** at ``/docs`` endpoint
- **Schema validation** for requests and responses
- **Code generation** support for client libraries

API Versioning
==============

Version Strategy

The API uses URL path versioning:

- **Current version**: ``/api/v1/``
- **Future versions**: ``/api/v2/``, ``/api/v3/``, etc.
- **Backward compatibility**: Previous versions maintained during transition
- **Deprecation policy**: 6-month notice before version retirement

Version Headers

Optional version specification via headers:

.. code-block:: text

    Accept: application/vnd.supplementtracker.v1+json
    API-Version: v1

URL Structure
=============

Base URL Pattern

.. code-block:: text

    https://api.supplementtracker.com/api/v1/{resource}

Resource Hierarchy

.. code-block:: text

    /api/v1/
    ├── auth/                    # Authentication endpoints
    │   ├── login
    │   ├── logout
    │   ├── refresh
    │   └── register
    ├── users/                   # User management
    │   ├── me
    │   ├── {user_id}
    │   └── {user_id}/profile
    ├── supplements/             # Supplement data
    │   ├── {supplement_id}
    │   ├── search
    │   └── categories
    ├── intakes/                 # Supplement intake tracking
    │   ├── {intake_id}
    │   ├── daily
    │   └── history
    ├── stacks/                  # Supplement stacks
    │   ├── {stack_id}
    │   └── {stack_id}/items
    ├── community/               # Community features (planned)
    │   ├── posts
    │   ├── discussions
    │   └── reviews
    └── research/                # Research tools (planned)
        ├── studies
        ├── correlations
        └── exports

HTTP Methods and Operations
===========================

Standard CRUD Operations

.. list-table::
   :header-rows: 1
   :widths: 15 15 35 35

   * - Method
     - Operation
     - Example
     - Description
   * - GET
     - Read
     - ``GET /api/v1/supplements``
     - Retrieve resource(s)
   * - POST
     - Create
     - ``POST /api/v1/supplements``
     - Create new resource
   * - PUT
     - Update
     - ``PUT /api/v1/supplements/{id}``
     - Update entire resource
   * - PATCH
     - Partial Update
     - ``PATCH /api/v1/supplements/{id}``
     - Update specific fields
   * - DELETE
     - Delete
     - ``DELETE /api/v1/supplements/{id}``
     - Remove resource

Special Operations

.. list-table::
   :header-rows: 1
   :widths: 15 35 50

   * - Method
     - Example
     - Description
   * - POST
     - ``POST /api/v1/auth/login``
     - Authentication actions
   * - GET
     - ``GET /api/v1/supplements/search?q=vitamin``
     - Search operations
   * - POST
     - ``POST /api/v1/intakes/bulk``
     - Bulk operations

Request/Response Format
=======================

Content Types

- **Request**: ``application/json`` (primary), ``multipart/form-data`` (file uploads)
- **Response**: ``application/json`` (primary), ``text/csv`` (exports)

Request Structure

Standard request format:

.. code-block:: json

    {
        "data": {
            "name": "Vitamin D3",
            "brand": "Nature's Way",
            "category": "Vitamin",
            "form": "Capsule"
        }
    }

Response Structure

Successful responses:

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "name": "Vitamin D3",
            "brand": "Nature's Way",
            "category": "Vitamin",
            "form": "Capsule",
            "created_at": "2025-06-18T15:30:00Z",
            "updated_at": "2025-06-18T15:30:00Z"
        },
        "meta": {
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

List responses with pagination:

.. code-block:: json

    {
        "data": [
            {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "Vitamin D3"
            }
        ],
        "pagination": {
            "total": 150,
            "page": 1,
            "per_page": 20,
            "pages": 8
        },
        "meta": {
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

Error Response Format
=====================

Standard Error Structure

.. code-block:: json

    {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Invalid input data",
            "details": {
                "field": "email",
                "issue": "Invalid email format"
            },
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

Error Codes

.. list-table::
   :header-rows: 1
   :widths: 20 15 65

   * - Error Code
     - HTTP Status
     - Description
   * - VALIDATION_ERROR
     - 400
     - Request data validation failed
   * - AUTHENTICATION_REQUIRED
     - 401
     - Authentication credentials required
   * - AUTHENTICATION_FAILED
     - 401
     - Invalid authentication credentials
   * - AUTHORIZATION_FAILED
     - 403
     - Insufficient permissions
   * - RESOURCE_NOT_FOUND
     - 404
     - Requested resource does not exist
   * - RESOURCE_CONFLICT
     - 409
     - Resource already exists or conflict
   * - RATE_LIMIT_EXCEEDED
     - 429
     - Too many requests
   * - INTERNAL_SERVER_ERROR
     - 500
     - Unexpected server error

Authentication and Authorization
================================
===============================

Authentication Methods

**JWT Bearer Token** (Primary):

.. code-block:: text

    Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

**API Key** (For integrations):

.. code-block:: text

    X-API-Key: sk_live_123456789abcdef

Token Structure

JWT tokens contain:

.. code-block:: json

    {
        "sub": "user_id",
        "exp": 1640995200,
        "iat": 1640908800,
        "type": "access",
        "permissions": ["read:supplements", "write:intakes"]
    }

Authorization Levels

- **Public**: No authentication required
- **User**: Authenticated user access
- **Admin**: Administrative privileges
- **System**: Internal system access

Pagination and Filtering
========================

Pagination Parameters

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Default
     - Description
   * - page
     - 1
     - Page number (1-based)
   * - per_page
     - 20
     - Items per page (max 100)
   * - sort
     - created_at
     - Sort field
   * - order
     - desc
     - Sort order (asc/desc)

Filtering Parameters

.. list-table::
   :header-rows: 1
   :widths: 25 75

   * - Parameter
     - Description
   * - q
     - Full-text search query
   * - category
     - Filter by category
   * - brand
     - Filter by brand
   * - created_after
     - Filter by creation date
   * - is_verified
     - Filter by verification status

Example Request:

.. code-block:: text

    GET /api/v1/supplements?page=2&per_page=10&category=vitamin&sort=name&order=asc

Rate Limiting
=============

Rate Limit Tiers

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Tier
     - Limit
     - Description
   * - Anonymous
     - 100/hour
     - Unauthenticated requests
   * - Authenticated
     - 1000/hour
     - Regular user requests
   * - Premium
     - 5000/hour
     - Premium user requests
   * - API Key
     - 10000/hour
     - Integration requests

Rate Limit Headers

.. code-block:: text

    X-RateLimit-Limit: 1000
    X-RateLimit-Remaining: 999
    X-RateLimit-Reset: 1640995200

Caching Strategy
================

Cache Headers

.. code-block:: text

    Cache-Control: public, max-age=3600
    ETag: "33a64df551425fcc55e4d42a148795d9f25f89d4"
    Last-Modified: Wed, 18 Jun 2025 15:30:00 GMT

Cache Policies

- **Static data**: 1 hour cache (supplements, categories)
- **User data**: No cache (private information)
- **Search results**: 15 minutes cache
- **API documentation**: 24 hours cache

API Documentation
=================

Interactive Documentation

Available at ``/docs`` endpoint:

- **Swagger UI**: Interactive API explorer
- **Request/response examples**: For each endpoint
- **Authentication testing**: Built-in auth support
- **Schema validation**: Real-time validation

ReDoc Documentation

Available at ``/redoc`` endpoint:

- **Clean documentation**: Professional appearance
- **Printable format**: PDF generation support
- **Code samples**: Multiple programming languages

OpenAPI Specification

Available at ``/openapi.json``:

- **Machine-readable**: For code generation
- **Client libraries**: Automatic generation
- **Testing tools**: Integration with testing frameworks

Webhook Support
===============

Webhook Events

Planned webhook events:

- ``user.created``: New user registration
- ``supplement.created``: New supplement added
- ``intake.logged``: Supplement intake recorded
- ``stack.created``: New supplement stack created

Webhook Format

.. code-block:: json

    {
        "event": "intake.logged",
        "data": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "user_id": "user_123",
            "supplement_id": "supp_456",
            "dosage": 1000,
            "dosage_unit": "mg",
            "taken_at": "2025-06-18T15:30:00Z"
        },
        "timestamp": "2025-06-18T15:30:00Z",
        "webhook_id": "wh_789"
    }

Security Considerations
=======================
======================

Input Validation

- **Pydantic schemas**: Automatic validation
- **Type checking**: Runtime type validation
- **Sanitization**: XSS prevention
- **Size limits**: Request body size limits

CORS Configuration

.. code-block:: python

    CORS_ORIGINS = [
        "https://supplementtracker.com",
        "https://app.supplementtracker.com",
        "http://localhost:3000"  # Development
    ]

Security Headers

.. code-block:: text

    X-Content-Type-Options: nosniff
    X-Frame-Options: DENY
    X-XSS-Protection: 1; mode=block
    Strict-Transport-Security: max-age=31536000; includeSubDomains

API Testing
===========

Test Categories

- **Unit tests**: Individual endpoint testing
- **Integration tests**: End-to-end workflows
- **Performance tests**: Load and stress testing
- **Security tests**: Vulnerability scanning

Test Data

- **Fixtures**: Consistent test data
- **Factories**: Dynamic test data generation
- **Mocking**: External service simulation
- **Cleanup**: Automatic test data cleanup

Client Libraries
================

Official SDKs

Planned official client libraries:

- **Python SDK**: For data science and research
- **JavaScript SDK**: For web applications
- **React SDK**: For React applications
- **Mobile SDKs**: iOS and Android native

Community SDKs

Community-maintained libraries:

- **R package**: For statistical analysis
- **Go client**: For backend integrations
- **PHP client**: For web applications

Migration and Versioning
========================

API Evolution

- **Additive changes**: New fields, endpoints (non-breaking)
- **Deprecation process**: 6-month notice period
- **Breaking changes**: New API version required
- **Documentation**: Change logs and migration guides

Backward Compatibility

- **Field additions**: Always optional
- **Field removals**: Deprecated first
- **Endpoint changes**: Version-specific
