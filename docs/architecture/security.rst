=====================
Security Architecture
=====================

This document outlines the comprehensive security measures implemented in the Supplement Tracker platform to protect user data, ensure system integrity, and maintain compliance with privacy regulations.

.. contents:: Table of Contents
   :local:
   :depth: 2

Security Overview
=================

Security Principles

The platform follows industry-standard security principles:

- **Defense in Depth**: Multiple layers of security controls
- **Principle of Least Privilege**: Minimal access rights by default
- **Zero Trust Architecture**: Verify every request and user
- **Security by Design**: Security considerations in every component
- **Continuous Monitoring**: Real-time threat detection and response

Compliance Standards

The platform is designed to comply with:

- **GDPR**: General Data Protection Regulation (EU)
- **HIPAA**: Health Insurance Portability and Accountability Act (US)
- **CCPA**: California Consumer Privacy Act (US)
- **SOC 2 Type II**: Security, availability, and confidentiality
- **ISO 27001**: Information security management

Authentication Architecture
===========================

JWT-Based Authentication

**Token Structure**:

.. code-block:: json

    {
        "header": {
            "alg": "HS256",
            "typ": "JWT"
        },
        "payload": {
            "sub": "user_id",
            "exp": **********,
            "iat": **********,
            "type": "access",
            "permissions": ["read:supplements", "write:intakes"]
        }
    }

**Security Features**:

- **Secure signing**: HMAC SHA-256 with strong secret key
- **Token expiration**: Short-lived access tokens (15 minutes)
- **Refresh tokens**: Longer-lived tokens for renewal (7 days)
- **Token revocation**: Blacklist support for compromised tokens

Multi-Factor Authentication (MFA)

**Supported Methods**:

- **TOTP**: Time-based One-Time Passwords (Google Authenticator, Authy)
- **SMS**: Text message verification (backup method)
- **Email**: Email-based verification codes
- **Hardware tokens**: FIDO2/WebAuthn support (planned)

**Implementation**:

.. code-block:: python

    class MFAService:
        async def generate_totp_secret(self, user_id: UUID) -> str:
            """Generate TOTP secret for user"""
            pass
        
        async def verify_totp(self, user_id: UUID, token: str) -> bool:
            """Verify TOTP token"""
            pass

Password Security

**Password Requirements**:

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character
- Not in common password lists

**Password Storage**:

.. code-block:: python

    from passlib.context import CryptContext
    
    pwd_context = CryptContext(
        schemes=["bcrypt"],
        deprecated="auto",
        bcrypt__rounds=12  # Strong work factor
    )

Authorization System
====================

Role-Based Access Control (RBAC)

**User Roles**:

.. list-table::
   :header-rows: 1
   :widths: 20 80

   * - Role
     - Permissions
   * - User
     - Read own data, create intakes, manage own supplements
   * - Moderator
     - Verify supplements, moderate community content
   * - Researcher
     - Access anonymized research data, export capabilities
   * - Admin
     - Full system access, user management
   * - System
     - Internal service operations

**Permission System**:

.. code-block:: python

    class Permission:
        READ_OWN_DATA = "read:own_data"
        WRITE_OWN_DATA = "write:own_data"
        READ_SUPPLEMENTS = "read:supplements"
        WRITE_SUPPLEMENTS = "write:supplements"
        MODERATE_CONTENT = "moderate:content"
        ACCESS_RESEARCH_DATA = "access:research_data"
        ADMIN_USERS = "admin:users"

Resource-Level Authorization

**Ownership Checks**:

.. code-block:: python

    async def check_resource_ownership(
        user: User, 
        resource_id: UUID, 
        resource_type: str
    ) -> bool:
        """Verify user owns the resource"""
        if resource_type == "intake":
            intake = await get_intake(resource_id)
            return intake.user_id == user.id
        # Additional resource checks...

**Context-Aware Permissions**:

- Users can only access their own data
- Moderators can access public content for moderation
- Researchers can access anonymized aggregate data
- Admins have full access with audit logging

Data Protection
===============

Encryption at Rest

**Database Encryption**:

- **Full disk encryption**: AES-256 encryption for database storage
- **Column-level encryption**: Sensitive fields (PII, health data)
- **Key management**: AWS KMS or HashiCorp Vault
- **Key rotation**: Automatic key rotation every 90 days

**File Storage Encryption**:

- **S3 encryption**: Server-side encryption with customer keys
- **Upload encryption**: Client-side encryption for sensitive files
- **Backup encryption**: Encrypted database backups

Encryption in Transit

**TLS Configuration**:

.. code-block:: nginx

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

**Certificate Management**:

- **Let's Encrypt**: Automatic certificate provisioning
- **Certificate pinning**: Mobile app certificate validation
- **HSTS**: HTTP Strict Transport Security headers

Data Anonymization

**Research Data Protection**:

.. code-block:: python

    class DataAnonymizer:
        async def anonymize_user_data(self, user_id: UUID) -> dict:
            """Convert user data to anonymous research format"""
            research_id = self.generate_research_id(user_id)
            return {
                "research_id": research_id,
                "age_range": self.get_age_range(user_id),
                "gender": self.get_gender(user_id),
                # Remove all PII
            }

**Anonymization Techniques**:

- **K-anonymity**: Ensure groups of at least k individuals
- **Differential privacy**: Add statistical noise to protect individuals
- **Data masking**: Replace sensitive values with realistic alternatives
- **Aggregation**: Provide only statistical summaries

Input Validation and Sanitization
=================================

Request Validation

**Pydantic Schemas**:

.. code-block:: python

    class SupplementCreate(BaseModel):
        name: str = Field(..., min_length=1, max_length=255)
        brand: Optional[str] = Field(None, max_length=255)
        category: str = Field(..., regex="^[A-Za-z0-9\\s-]+$")
        
        @validator('name')
        def validate_name(cls, v):
            # Custom validation logic
            return v.strip()

**Input Sanitization**:

- **HTML sanitization**: Remove malicious HTML/JavaScript
- **SQL injection prevention**: Parameterized queries only
- **Path traversal prevention**: Validate file paths
- **Command injection prevention**: No shell command execution

Rate Limiting and DDoS Protection
=================================

Rate Limiting Strategy

**Tiered Rate Limits**:

.. code-block:: python

    RATE_LIMITS = {
        "anonymous": "100/hour",
        "authenticated": "1000/hour",
        "premium": "5000/hour",
        "api_key": "10000/hour"
    }

**Adaptive Rate Limiting**:

- **Behavioral analysis**: Detect unusual usage patterns
- **Geographic limiting**: Higher limits for trusted regions
- **Endpoint-specific limits**: Different limits per endpoint type
- **Burst protection**: Allow short bursts with recovery time

DDoS Protection

**Infrastructure Level**:

- **CDN protection**: Cloudflare or AWS CloudFront
- **Load balancer filtering**: Drop malicious requests
- **IP reputation**: Block known malicious IPs
- **Geographic blocking**: Block traffic from high-risk regions

**Application Level**:

- **Request validation**: Reject malformed requests early
- **Resource limiting**: Limit concurrent connections per IP
- **Circuit breakers**: Fail fast under high load
- **Graceful degradation**: Maintain core functionality under attack

Session Management
==================

Session Security

**Session Configuration**:

.. code-block:: python

    SESSION_CONFIG = {
        "secure": True,          # HTTPS only
        "httponly": True,        # No JavaScript access
        "samesite": "strict",    # CSRF protection
        "max_age": 3600,         # 1 hour expiration
        "domain": ".supplementtracker.com"
    }

**Session Storage**:

- **Redis storage**: Centralized session management
- **Session encryption**: Encrypted session data
- **Session invalidation**: Logout and timeout handling
- **Concurrent session limits**: Maximum active sessions per user

CSRF Protection

**Token-Based Protection**:

.. code-block:: python

    class CSRFProtection:
        def generate_token(self, session_id: str) -> str:
            """Generate CSRF token for session"""
            pass
        
        def validate_token(self, token: str, session_id: str) -> bool:
            """Validate CSRF token"""
            pass

**SameSite Cookies**:

- **Strict mode**: Maximum protection for sensitive operations
- **Lax mode**: Balance between security and usability
- **None mode**: Only for cross-site integrations with HTTPS

API Security
============

API Key Management

**Key Generation**:

.. code-block:: python

    class APIKeyService:
        def generate_key(self, user_id: UUID, permissions: List[str]) -> str:
            """Generate API key with specific permissions"""
            prefix = "sk_live_" if self.is_production else "sk_test_"
            key = secrets.token_urlsafe(32)
            return f"{prefix}{key}"

**Key Security**:

- **Secure generation**: Cryptographically secure random keys
- **Permission scoping**: Limit key permissions to minimum required
- **Key rotation**: Regular key rotation recommendations
- **Key revocation**: Immediate key deactivation capability

Request Signing

**HMAC Signatures**:

.. code-block:: python

    def verify_signature(payload: bytes, signature: str, secret: str) -> bool:
        """Verify HMAC signature for webhook/API requests"""
        expected = hmac.new(
            secret.encode(),
            payload,
            hashlib.sha256
        ).hexdigest()
        return hmac.compare_digest(signature, expected)

Audit Logging and Monitoring
============================

Security Event Logging

**Logged Events**:

- Authentication attempts (success/failure)
- Authorization failures
- Data access and modifications
- Administrative actions
- Security configuration changes
- Suspicious activity patterns

**Log Format**:

.. code-block:: json

    {
        "timestamp": "2025-06-18T15:30:00Z",
        "event_type": "authentication_failure",
        "user_id": "user_123",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0...",
        "details": {
            "reason": "invalid_password",
            "attempt_count": 3
        },
        "risk_score": 7.5
    }

Real-Time Monitoring

**Security Metrics**:

- Failed authentication attempts
- Unusual access patterns
- Data export activities
- Administrative actions
- API usage anomalies

**Alerting System**:

- **Immediate alerts**: Critical security events
- **Threshold alerts**: Unusual activity patterns
- **Trend alerts**: Gradual security degradation
- **Compliance alerts**: Regulatory requirement violations

Incident Response
=================

Security Incident Classification

.. list-table::
   :header-rows: 1
   :widths: 15 25 60

   * - Severity
     - Response Time
     - Examples
   * - Critical
     - < 1 hour
     - Data breach, system compromise
   * - High
     - < 4 hours
     - Authentication bypass, privilege escalation
   * - Medium
     - < 24 hours
     - Suspicious activity, failed attacks
   * - Low
     - < 72 hours
     - Policy violations, minor vulnerabilities

Response Procedures

**Incident Response Team**:

- **Security Lead**: Overall incident coordination
- **Technical Lead**: System analysis and remediation
- **Legal Counsel**: Compliance and notification requirements
- **Communications**: User and stakeholder communication

**Response Steps**:

1. **Detection and Analysis**: Identify and assess the incident
2. **Containment**: Limit the scope and impact
3. **Eradication**: Remove the threat from the system
4. **Recovery**: Restore normal operations
5. **Lessons Learned**: Post-incident review and improvements

Compliance and Privacy
======================
=====================

GDPR Compliance

**Data Subject Rights**:

- **Right to access**: User data export functionality
- **Right to rectification**: Data correction capabilities
- **Right to erasure**: Account and data deletion
- **Right to portability**: Data export in standard formats
- **Right to object**: Opt-out of data processing

**Privacy by Design**:

- **Data minimization**: Collect only necessary data
- **Purpose limitation**: Use data only for stated purposes
- **Storage limitation**: Retain data only as long as necessary
- **Accuracy**: Maintain accurate and up-to-date data

HIPAA Considerations

**Health Data Protection**:

- **Minimum necessary**: Access only required health data
- **Audit trails**: Comprehensive access logging
- **Data encryption**: Protect health information at rest and in transit
- **Business associate agreements**: Third-party service contracts

**Technical Safeguards**:

- **Access controls**: Role-based health data access
- **Audit controls**: Monitor health data access
- **Integrity controls**: Prevent unauthorized health data modification
- **Transmission security**: Secure health data transmission

Security Testing
================

Vulnerability Assessment

**Regular Testing**:

- **Automated scanning**: Daily vulnerability scans
- **Penetration testing**: Quarterly professional assessments
- **Code review**: Security-focused code reviews
- **Dependency scanning**: Third-party library vulnerability checks

**Testing Scope**:

- **Web application**: OWASP Top 10 vulnerabilities
- **API security**: Authentication and authorization flaws
- **Infrastructure**: Server and network security
- **Mobile applications**: Mobile-specific security issues

Security Training
=================
================

Developer Training

**Security Awareness**:

- **Secure coding practices**: OWASP guidelines
- **Threat modeling**: Identify potential security risks
- **Security testing**: Integration of security tests
- **Incident response**: Developer role in security incidents

**Ongoing Education**:

- **Monthly security updates**: Latest threat intelligence
- **Security workshops**: Hands-on security training
- **Certification support**: Security certification programs
