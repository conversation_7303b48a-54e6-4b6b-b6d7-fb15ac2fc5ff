===============================================
Monitoring and Observability
Monitoring and Observability
===============================================

This document describes the comprehensive monitoring, logging, and observability strategy for the Supplement Tracker platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Observability Overview
======================

Three Pillars of Observability
------------------------------

**Metrics**
   Quantitative measurements of system behavior over time

**Logs**
   Discrete events that occurred within the system

**Traces**
   Request flows through distributed system components

Monitoring Philosophy
---------------------
--------------------

- **Proactive Monitoring**: Detect issues before they impact users
- **User-Centric Metrics**: Focus on user experience and business outcomes
- **Actionable Alerts**: Every alert should require immediate action
- **Context-Rich Data**: Provide sufficient context for troubleshooting
- **Continuous Improvement**: Regularly review and refine monitoring

Metrics and Monitoring
======================

Application Metrics
-------------------

**Golden Signals**:

.. list-table::
   :header-rows: 1
   :widths: 25 75

   * - Signal
     - Description
   * - Latency
     - Time to process requests (p50, p95, p99)
   * - Traffic
     - Request rate (requests per second)
   * - Errors
     - Error rate (percentage of failed requests)
   * - Saturation
     - Resource utilization (CPU, memory, disk)

**Custom Business Metrics**:

.. code-block:: python

    from prometheus_client import Counter, Histogram, Gauge

    # User engagement metrics
    user_registrations = Counter('user_registrations_total', 'Total user registrations')
    user_logins = Counter('user_logins_total', 'Total user logins')
    active_users = Gauge('active_users', 'Currently active users')

    # Supplement tracking metrics
    supplements_created = Counter('supplements_created_total', 'Total supplements created')
    intakes_logged = Counter('intakes_logged_total', 'Total supplement intakes logged')
    
    # API performance metrics
    request_duration = Histogram(
        'http_request_duration_seconds',
        'HTTP request duration',
        ['method', 'endpoint', 'status']
    )

Infrastructure Metrics
----------------------

**System Metrics**:

- **CPU Usage**: Per-core and aggregate utilization
- **Memory Usage**: Available, used, and cached memory
- **Disk I/O**: Read/write operations and throughput
- **Network I/O**: Inbound/outbound traffic and packet loss

**Database Metrics**:

- **Connection Pool**: Active, idle, and waiting connections
- **Query Performance**: Slow queries and execution times
- **Lock Contention**: Blocking queries and deadlocks
- **Replication Lag**: Delay between primary and replicas

**Cache Metrics**:

- **Hit Rate**: Cache hit/miss ratio
- **Memory Usage**: Redis memory consumption
- **Key Expiration**: TTL and eviction patterns
- **Connection Count**: Active Redis connections

Prometheus Configuration
------------------------
-----------------------

**Prometheus Setup**:

.. code-block:: yaml

    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "alert_rules.yml"

    scrape_configs:
      - job_name: 'supplement-tracker-api'
        static_configs:
          - targets: ['api:8000']
        metrics_path: /metrics
        scrape_interval: 5s

      - job_name: 'postgresql'
        static_configs:
          - targets: ['postgres-exporter:9187']

      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']

      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true

**Alert Rules**:

.. code-block:: yaml

    groups:
      - name: supplement-tracker-alerts
        rules:
          - alert: HighErrorRate
            expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "High error rate detected"
              description: "Error rate is {{ $value }} errors per second"

          - alert: HighLatency
            expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
            for: 10m
            labels:
              severity: warning
            annotations:
              summary: "High latency detected"
              description: "95th percentile latency is {{ $value }} seconds"

          - alert: DatabaseConnectionsHigh
            expr: pg_stat_activity_count > 80
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High database connection count"
              description: "Database has {{ $value }} active connections"

Grafana Dashboards
==================

Application Dashboard
---------------------
--------------------

**Key Panels**:

1. **Request Rate**: Requests per second over time
2. **Response Time**: P50, P95, P99 latencies
3. **Error Rate**: Percentage of failed requests
4. **Active Users**: Currently authenticated users
5. **Business Metrics**: Registrations, intakes, supplements

**Dashboard JSON**:

.. code-block:: json

    {
      "dashboard": {
        "title": "Supplement Tracker - Application Metrics",
        "panels": [
          {
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(http_requests_total[5m])",
                "legendFormat": "{{method}} {{endpoint}}"
              }
            ]
          },
          {
            "title": "Response Time",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
                "legendFormat": "P50"
              },
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
                "legendFormat": "P95"
              }
            ]
          }
        ]
      }
    }

Infrastructure Dashboard
------------------------
-----------------------

**System Overview**:

- **CPU Usage**: Multi-core utilization graphs
- **Memory Usage**: Available vs used memory
- **Disk I/O**: Read/write operations per second
- **Network Traffic**: Inbound/outbound bandwidth

**Database Dashboard**:

- **Connection Pool**: Active/idle connections
- **Query Performance**: Slow query log analysis
- **Cache Hit Rate**: Query result caching efficiency
- **Replication Status**: Primary/replica synchronization

Logging Strategy
================

Structured Logging
------------------

**Log Format**:

.. code-block:: python

    import structlog

    logger = structlog.get_logger()

    # Structured log entry
    logger.info(
        "User authentication successful",
        user_id="123e4567-e89b-12d3-a456-426614174000",
        ip_address="*************",
        user_agent="Mozilla/5.0...",
        duration_ms=150,
        event_type="authentication",
        success=True
    )

**Log Levels**:

.. list-table::
   :header-rows: 1
   :widths: 15 85

   * - Level
     - Usage
   * - DEBUG
     - Detailed diagnostic information
   * - INFO
     - General operational messages
   * - WARNING
     - Potentially harmful situations
   * - ERROR
     - Error events that don't stop execution
   * - CRITICAL
     - Serious errors that may cause termination

Centralized Logging
-------------------

**ELK Stack Configuration**:

.. code-block:: yaml

    # Elasticsearch
    apiVersion: apps/v1
    kind: StatefulSet
    metadata:
      name: elasticsearch
    spec:
      serviceName: elasticsearch
      replicas: 3
      template:
        spec:
          containers:
          - name: elasticsearch
            image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
            env:
            - name: discovery.type
              value: zen
            - name: ES_JAVA_OPTS
              value: "-Xms2g -Xmx2g"

    # Logstash
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: logstash
    spec:
      template:
        spec:
          containers:
          - name: logstash
            image: docker.elastic.co/logstash/logstash:8.8.0
            volumeMounts:
            - name: logstash-config
              mountPath: /usr/share/logstash/pipeline

    # Kibana
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: kibana
    spec:
      template:
        spec:
          containers:
          - name: kibana
            image: docker.elastic.co/kibana/kibana:8.8.0
            env:
            - name: ELASTICSEARCH_HOSTS
              value: "http://elasticsearch:9200"

**Logstash Pipeline**:

.. code-block:: ruby

    input {
      beats {
        port => 5044
      }
    }

    filter {
      if [kubernetes][container][name] == "supplement-tracker-api" {
        json {
          source => "message"
        }
        
        date {
          match => [ "timestamp", "ISO8601" ]
        }
        
        mutate {
          add_field => { "service" => "supplement-tracker-api" }
        }
      }
    }

    output {
      elasticsearch {
        hosts => ["elasticsearch:9200"]
        index => "supplement-tracker-%{+YYYY.MM.dd}"
      }
    }

Log Aggregation
---------------

**Filebeat Configuration**:

.. code-block:: yaml

    filebeat.inputs:
    - type: container
      paths:
        - /var/log/containers/*supplement-tracker*.log
      processors:
      - add_kubernetes_metadata:
          host: ${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"

    output.logstash:
      hosts: ["logstash:5044"]

    logging.level: info
    logging.to_files: true
    logging.files:
      path: /var/log/filebeat
      name: filebeat
      keepfiles: 7
      permissions: 0644

Distributed Tracing
===================

OpenTelemetry Integration
-------------------------
------------------------

**Tracing Setup**:

.. code-block:: python

    from opentelemetry import trace
    from opentelemetry.exporter.jaeger.thrift import JaegerExporter
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor

    # Configure tracing
    trace.set_tracer_provider(TracerProvider())
    tracer = trace.get_tracer(__name__)

    jaeger_exporter = JaegerExporter(
        agent_host_name="jaeger",
        agent_port=6831,
    )

    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)

**Instrumentation**:

.. code-block:: python

    @tracer.start_as_current_span("create_supplement")
    async def create_supplement(supplement_data: SupplementCreate) -> Supplement:
        span = trace.get_current_span()
        span.set_attribute("supplement.name", supplement_data.name)
        span.set_attribute("supplement.category", supplement_data.category)
        
        try:
            # Business logic
            supplement = await supplement_service.create(supplement_data)
            span.set_attribute("supplement.id", str(supplement.id))
            span.set_status(trace.Status(trace.StatusCode.OK))
            return supplement
        except Exception as e:
            span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
            span.record_exception(e)
            raise

Jaeger Configuration
--------------------
-------------------

.. code-block:: yaml

    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: jaeger
    spec:
      template:
        spec:
          containers:
          - name: jaeger
            image: jaegertracing/all-in-one:1.45
            env:
            - name: COLLECTOR_ZIPKIN_HOST_PORT
              value: ":9411"
            ports:
            - containerPort: 16686
              name: ui
            - containerPort: 14268
              name: collector

Alerting and Notifications
==========================

Alertmanager Configuration
--------------------------
-------------------------

.. code-block:: yaml

    global:
      smtp_smarthost: 'smtp.gmail.com:587'
      smtp_from: '<EMAIL>'

    route:
      group_by: ['alertname']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'web.hook'
      routes:
      - match:
          severity: critical
        receiver: 'critical-alerts'
      - match:
          severity: warning
        receiver: 'warning-alerts'

    receivers:
    - name: 'web.hook'
      webhook_configs:
      - url: 'http://slack-webhook/alerts'

    - name: 'critical-alerts'
      email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
      slack_configs:
      - api_url: 'https://hooks.slack.com/services/...'
        channel: '#alerts-critical'
        title: 'Critical Alert'
        text: '{{ .CommonAnnotations.summary }}'

    - name: 'warning-alerts'
      slack_configs:
      - api_url: 'https://hooks.slack.com/services/...'
        channel: '#alerts-warning'
        title: 'Warning Alert'
        text: '{{ .CommonAnnotations.summary }}'

Notification Channels
---------------------
--------------------

**Slack Integration**:

.. code-block:: python

    import slack_sdk

    class SlackNotifier:
        def __init__(self, token: str):
            self.client = slack_sdk.WebClient(token=token)
        
        async def send_alert(self, channel: str, alert: dict):
            message = {
                "channel": channel,
                "blocks": [
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*{alert['severity'].upper()}*: {alert['summary']}"
                        }
                    },
                    {
                        "type": "section",
                        "fields": [
                            {
                                "type": "mrkdwn",
                                "text": f"*Service:*\n{alert['service']}"
                            },
                            {
                                "type": "mrkdwn",
                                "text": f"*Time:*\n{alert['timestamp']}"
                            }
                        ]
                    }
                ]
            }
            
            await self.client.chat_postMessage(**message)

**PagerDuty Integration**:

.. code-block:: python

    import pypd

    class PagerDutyNotifier:
        def __init__(self, integration_key: str):
            self.integration_key = integration_key
        
        async def trigger_incident(self, alert: dict):
            incident = pypd.EventV2.create(
                data={
                    'routing_key': self.integration_key,
                    'event_action': 'trigger',
                    'payload': {
                        'summary': alert['summary'],
                        'severity': alert['severity'],
                        'source': alert['service'],
                        'custom_details': alert['details']
                    }
                }
            )
            return incident

Performance Monitoring
======================

Application Performance Monitoring (APM)
----------------------------------------

**Key Metrics**:

- **Apdex Score**: Application performance index
- **Throughput**: Requests per minute
- **Response Time**: Average and percentile response times
- **Error Rate**: Percentage of failed requests
- **Database Performance**: Query execution times

**New Relic Integration**:

.. code-block:: python

    import newrelic.agent

    @newrelic.agent.function_trace()
    async def create_supplement(supplement_data: SupplementCreate):
        # Add custom attributes
        newrelic.agent.add_custom_attribute('supplement.category', supplement_data.category)
        newrelic.agent.add_custom_attribute('user.type', 'authenticated')
        
        # Business logic
        return await supplement_service.create(supplement_data)

Real User Monitoring (RUM)
--------------------------

**Frontend Monitoring**:

.. code-block:: javascript

    // Browser performance monitoring
    import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

    function sendToAnalytics(metric) {
      fetch('/api/v1/analytics/web-vitals', {
        method: 'POST',
        body: JSON.stringify(metric),
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    getCLS(sendToAnalytics);
    getFID(sendToAnalytics);
    getFCP(sendToAnalytics);
    getLCP(sendToAnalytics);
    getTTFB(sendToAnalytics);

Synthetic Monitoring
--------------------

**Health Check Monitoring**:

.. code-block:: python

    import aiohttp
    import asyncio

    class SyntheticMonitor:
        async def check_api_health(self):
            async with aiohttp.ClientSession() as session:
                start_time = asyncio.get_event_loop().time()
                
                try:
                    async with session.get('https://api.supplementtracker.com/health') as response:
                        end_time = asyncio.get_event_loop().time()
                        
                        return {
                            'status': 'up' if response.status == 200 else 'down',
                            'response_time': end_time - start_time,
                            'status_code': response.status
                        }
                except Exception as e:
                    return {
                        'status': 'down',
                        'error': str(e)
                    }

Security Monitoring
===================
==================

Security Event Monitoring
-------------------------

**SIEM Integration**:

.. code-block:: python

    class SecurityEventLogger:
        def log_authentication_failure(self, user_id: str, ip_address: str, reason: str):
            logger.warning(
                "Authentication failure",
                event_type="security.auth_failure",
                user_id=user_id,
                ip_address=ip_address,
                failure_reason=reason,
                risk_score=self.calculate_risk_score(ip_address, reason)
            )
        
        def log_suspicious_activity(self, user_id: str, activity: str, details: dict):
            logger.error(
                "Suspicious activity detected",
                event_type="security.suspicious_activity",
                user_id=user_id,
                activity=activity,
                details=details,
                risk_score=8.5
            )

**Anomaly Detection**:

- **Failed login attempts**: Detect brute force attacks
- **Unusual access patterns**: Identify compromised accounts
- **Data export activities**: Monitor bulk data access
- **Geographic anomalies**: Detect impossible travel scenarios

Compliance Monitoring
---------------------
--------------------

**GDPR Compliance**:

- **Data access logging**: Track personal data access
- **Consent management**: Monitor consent status changes
- **Data retention**: Alert on data retention policy violations
- **Right to be forgotten**: Track data deletion requests

**Audit Trail**:

.. code-block:: python

    class AuditLogger:
        def log_data_access(self, user_id: str, accessed_user_id: str, data_type: str):
            logger.info(
                "Personal data accessed",
                event_type="audit.data_access",
                accessor_user_id=user_id,
                accessed_user_id=accessed_user_id,
                data_type=data_type,
                compliance_relevant=True
            )

Monitoring Best Practices
=========================

Alert Fatigue Prevention
------------------------

- **Meaningful alerts**: Every alert should be actionable
- **Alert grouping**: Combine related alerts
- **Escalation policies**: Progressive notification levels
- **Alert suppression**: Prevent duplicate notifications

Dashboard Design
----------------
---------------

- **User-focused metrics**: Start with user experience
- **Hierarchical information**: Overview to detailed views
- **Consistent time ranges**: Synchronized time windows
- **Contextual information**: Include relevant metadata

Monitoring as Code
------------------
-----------------

- **Version control**: Store monitoring configuration in Git
- **Automated deployment**: Deploy monitoring with applications
- **Testing**: Validate monitoring configuration
