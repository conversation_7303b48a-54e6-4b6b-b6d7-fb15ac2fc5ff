=======================
Deployment Architecture
=======================

This document describes the deployment strategies, infrastructure setup, and operational procedures for the Supplement Tracker platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Deployment Overview
===================

Deployment Strategy

The platform uses a **containerized microservices-ready architecture** with the following principles:

- **Infrastructure as Code**: All infrastructure defined in version control
- **Immutable Infrastructure**: Replace rather than modify infrastructure
- **Blue-Green Deployments**: Zero-downtime deployments
- **Automated Pipelines**: Continuous integration and deployment
- **Environment Parity**: Consistent environments across dev/staging/production

Deployment Environments

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Environment
     - Purpose
     - Configuration
   * - Development
     - Local development
     - Docker Compose, local databases
   * - Testing
     - Automated testing
     - Kubernetes, test databases
   * - Staging
     - Pre-production testing
     - Production-like setup, staging data
   * - Production
     - Live system
     - High availability, production data

Containerization
================

Docker Configuration

**Multi-stage Dockerfile**:

.. code-block:: dockerfile

    # Build stage
    FROM python:3.11-slim as builder
    WORKDIR /app
    COPY requirements.txt .
    RUN pip install --no-cache-dir -r requirements.txt

    # Production stage
    FROM python:3.11-slim
    WORKDIR /app
    COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
    COPY . .
    EXPOSE 8000
    CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]

**Docker Compose for Development**:

.. code-block:: yaml

    version: '3.8'
    services:
      app:
        build: .
        ports:
          - "8000:8000"
        environment:
          - DATABASE_URL=******************************/supplement_tracker
          - REDIS_URL=redis://redis:6379/0
        depends_on:
          - db
          - redis

      db:
        image: postgres:15
        environment:
          POSTGRES_DB: supplement_tracker
          POSTGRES_USER: user
          POSTGRES_PASSWORD: pass
        volumes:
          - postgres_data:/var/lib/postgresql/data

      redis:
        image: redis:7-alpine
        volumes:
          - redis_data:/data

    volumes:
      postgres_data:
      redis_data:

Container Security

**Security Best Practices**:

- **Non-root user**: Run containers as non-privileged user
- **Minimal base images**: Use slim or alpine base images
- **Vulnerability scanning**: Regular container image scanning
- **Secret management**: Use external secret management systems
- **Resource limits**: Set CPU and memory limits

.. code-block:: dockerfile

    # Create non-root user
    RUN adduser --disabled-password --gecos '' appuser
    USER appuser

    # Set resource limits in deployment
    resources:
      limits:
        cpu: "1"
        memory: "1Gi"
      requests:
        cpu: "500m"
        memory: "512Mi"

Kubernetes Deployment
=====================

Cluster Architecture

**Production Cluster Setup**:

.. code-block:: text

    Production Kubernetes Cluster
    ├── Control Plane (3 nodes)
    │   ├── API Server
    │   ├── etcd
    │   └── Controller Manager
    ├── Worker Nodes (6+ nodes)
    │   ├── Application Pods
    │   ├── Database Pods
    │   └── Monitoring Pods
    └── Load Balancer
        ├── Ingress Controller
        └── SSL Termination

**Namespace Organization**:

.. code-block:: yaml

    # Namespaces
    - supplement-tracker-prod
    - supplement-tracker-staging
    - monitoring
    - ingress-system
    - cert-manager

Application Deployment

**Deployment Manifest**:

.. code-block:: yaml

    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: supplement-tracker-api
      namespace: supplement-tracker-prod
    spec:
      replicas: 3
      selector:
        matchLabels:
          app: supplement-tracker-api
      template:
        metadata:
          labels:
            app: supplement-tracker-api
        spec:
          containers:
          - name: api
            image: supplement-tracker:latest
            ports:
            - containerPort: 8000
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: database-secret
                  key: url
            resources:
              requests:
                cpu: 500m
                memory: 512Mi
              limits:
                cpu: 1
                memory: 1Gi
            livenessProbe:
              httpGet:
                path: /health
                port: 8000
              initialDelaySeconds: 30
              periodSeconds: 10
            readinessProbe:
              httpGet:
                path: /ready
                port: 8000
              initialDelaySeconds: 5
              periodSeconds: 5

**Service Configuration**:

.. code-block:: yaml

    apiVersion: v1
    kind: Service
    metadata:
      name: supplement-tracker-api-service
    spec:
      selector:
        app: supplement-tracker-api
      ports:
      - protocol: TCP
        port: 80
        targetPort: 8000
      type: ClusterIP

Database Deployment

**PostgreSQL StatefulSet**:

.. code-block:: yaml

    apiVersion: apps/v1
    kind: StatefulSet
    metadata:
      name: postgresql
    spec:
      serviceName: postgresql
      replicas: 1
      selector:
        matchLabels:
          app: postgresql
      template:
        metadata:
          labels:
            app: postgresql
        spec:
          containers:
          - name: postgresql
            image: postgres:15
            env:
            - name: POSTGRES_DB
              value: supplement_tracker
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: username
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secret
                  key: password
            volumeMounts:
            - name: postgresql-storage
              mountPath: /var/lib/postgresql/data
      volumeClaimTemplates:
      - metadata:
          name: postgresql-storage
        spec:
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 100Gi

Ingress and Load Balancing
==========================

Ingress Configuration

**NGINX Ingress Controller**:

.. code-block:: yaml

    apiVersion: networking.k8s.io/v1
    kind: Ingress
    metadata:
      name: supplement-tracker-ingress
      annotations:
        kubernetes.io/ingress.class: nginx
        cert-manager.io/cluster-issuer: letsencrypt-prod
        nginx.ingress.kubernetes.io/rate-limit: "100"
        nginx.ingress.kubernetes.io/ssl-redirect: "true"
    spec:
      tls:
      - hosts:
        - api.supplementtracker.com
        secretName: supplement-tracker-tls
      rules:
      - host: api.supplementtracker.com
        http:
          paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: supplement-tracker-api-service
                port:
                  number: 80

SSL/TLS Configuration

**Cert-Manager for Automatic Certificates**:

.. code-block:: yaml

    apiVersion: cert-manager.io/v1
    kind: ClusterIssuer
    metadata:
      name: letsencrypt-prod
    spec:
      acme:
        server: https://acme-v02.api.letsencrypt.org/directory
        email: <EMAIL>
        privateKeySecretRef:
          name: letsencrypt-prod
        solvers:
        - http01:
            ingress:
              class: nginx

CI/CD Pipeline
==============

GitHub Actions Workflow

.. code-block:: yaml

    name: CI/CD Pipeline
    on:
      push:
        branches: [main, develop]
      pull_request:
        branches: [main]

    jobs:
      test:
        runs-on: ubuntu-latest
        steps:
        - uses: actions/checkout@v3
        - name: Set up Python
          uses: actions/setup-python@v4
          with:
            python-version: '3.11'
        - name: Install dependencies
          run: |
            pip install -r requirements.txt
            pip install -r requirements-dev.txt
        - name: Run tests
          run: |
            pytest --cov=app --cov-report=xml
        - name: Upload coverage
          uses: codecov/codecov-action@v3

      build:
        needs: test
        runs-on: ubuntu-latest
        if: github.ref == 'refs/heads/main'
        steps:
        - uses: actions/checkout@v3
        - name: Build Docker image
          run: |
            docker build -t supplement-tracker:${{ github.sha }} .
            docker tag supplement-tracker:${{ github.sha }} supplement-tracker:latest
        - name: Push to registry
          run: |
            echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
            docker push supplement-tracker:${{ github.sha }}
            docker push supplement-tracker:latest

      deploy:
        needs: build
        runs-on: ubuntu-latest
        if: github.ref == 'refs/heads/main'
        steps:
        - name: Deploy to Kubernetes
          run: |
            kubectl set image deployment/supplement-tracker-api supplement-tracker-api=supplement-tracker:${{ github.sha }}
            kubectl rollout status deployment/supplement-tracker-api

Deployment Strategies
=====================
====================

Blue-Green Deployment

**Process**:

1. **Prepare Green Environment**: Deploy new version to green environment
2. **Health Checks**: Verify green environment is healthy
3. **Switch Traffic**: Route traffic from blue to green
4. **Monitor**: Watch for issues in green environment
5. **Cleanup**: Decommission blue environment after successful deployment

**Kubernetes Implementation**:

.. code-block:: bash

    # Deploy to green environment
    kubectl apply -f deployment-green.yaml
    
    # Wait for rollout to complete
    kubectl rollout status deployment/supplement-tracker-api-green
    
    # Switch service to green deployment
    kubectl patch service supplement-tracker-api-service -p '{"spec":{"selector":{"version":"green"}}}'
    
    # Monitor and cleanup blue deployment
    kubectl delete deployment supplement-tracker-api-blue

Canary Deployment

**Gradual Traffic Shifting**:

.. code-block:: yaml

    # Canary deployment with 10% traffic
    apiVersion: argoproj.io/v1alpha1
    kind: Rollout
    metadata:
      name: supplement-tracker-api
    spec:
      replicas: 5
      strategy:
        canary:
          steps:
          - setWeight: 10
          - pause: {duration: 5m}
          - setWeight: 25
          - pause: {duration: 5m}
          - setWeight: 50
          - pause: {duration: 5m}
          - setWeight: 75
          - pause: {duration: 5m}

Infrastructure as Code
======================
=====================

Terraform Configuration

**AWS Infrastructure**:

.. code-block:: text

    # VPC Configuration
    resource "aws_vpc" "main" {
      cidr_block           = "10.0.0.0/16"
      enable_dns_hostnames = true
      enable_dns_support   = true
      
      tags = {
        Name = "supplement-tracker-vpc"
      }
    }

    # EKS Cluster
    resource "aws_eks_cluster" "main" {
      name     = "supplement-tracker"
      role_arn = aws_iam_role.cluster.arn
      version  = "1.27"

      vpc_config {
        subnet_ids = aws_subnet.private[*].id
      }

      depends_on = [
        aws_iam_role_policy_attachment.cluster_AmazonEKSClusterPolicy,
      ]
    }

    # RDS Database
    resource "aws_db_instance" "main" {
      identifier = "supplement-tracker-db"
      
      engine         = "postgres"
      engine_version = "15.3"
      instance_class = "db.t3.medium"
      
      allocated_storage     = 100
      max_allocated_storage = 1000
      storage_encrypted     = true
      
      db_name  = "supplement_tracker"
      username = var.db_username
      password = var.db_password
      
      vpc_security_group_ids = [aws_security_group.rds.id]
      db_subnet_group_name   = aws_db_subnet_group.main.name
      
      backup_retention_period = 7
      backup_window          = "03:00-04:00"
      maintenance_window     = "sun:04:00-sun:05:00"
      
      skip_final_snapshot = false
      final_snapshot_identifier = "supplement-tracker-final-snapshot"
      
      tags = {
        Name = "supplement-tracker-db"
      }
    }

Monitoring and Observability
============================

Application Monitoring

**Prometheus Configuration**:

.. code-block:: yaml

    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: prometheus-config
    data:
      prometheus.yml: |
        global:
          scrape_interval: 15s
        scrape_configs:
        - job_name: 'supplement-tracker-api'
          static_configs:
          - targets: ['supplement-tracker-api-service:80']
          metrics_path: /metrics
          scrape_interval: 5s

**Grafana Dashboards**:

- **Application Metrics**: Request rate, response time, error rate
- **Infrastructure Metrics**: CPU, memory, disk usage
- **Business Metrics**: User registrations, supplement intakes
- **Security Metrics**: Authentication failures, suspicious activity

Logging Strategy

**Centralized Logging with ELK Stack**:

.. code-block:: yaml

    # Filebeat configuration
    filebeat.inputs:
    - type: container
      paths:
        - /var/log/containers/*.log
      processors:
      - add_kubernetes_metadata:
          host: ${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"

    output.elasticsearch:
      hosts: ["elasticsearch:9200"]
      index: "supplement-tracker-%{+yyyy.MM.dd}"

Health Checks and Probes

**Application Health Endpoints**:

.. code-block:: python

    @app.get("/health")
    async def health_check():
        """Basic health check endpoint"""
        return {"status": "healthy", "timestamp": datetime.utcnow()}

    @app.get("/ready")
    async def readiness_check():
        """Readiness check with dependency validation"""
        try:
            # Check database connection
            await database.execute("SELECT 1")
            # Check Redis connection
            await redis.ping()
            return {"status": "ready", "timestamp": datetime.utcnow()}
        except Exception as e:
            raise HTTPException(status_code=503, detail="Service not ready")

Backup and Disaster Recovery
============================

Database Backup Strategy

**Automated Backups**:

- **Daily full backups**: Complete database backup
- **Continuous WAL archiving**: Point-in-time recovery
- **Cross-region replication**: Disaster recovery
- **Backup verification**: Automated restore testing

**Backup Configuration**:

.. code-block:: bash

    # PostgreSQL backup script
    #!/bin/bash
    BACKUP_DIR="/backups"
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    
    # Create backup
    pg_dump -h $DB_HOST -U $DB_USER -d supplement_tracker \
            -f "$BACKUP_DIR/supplement_tracker_$TIMESTAMP.sql"
    
    # Compress backup
    gzip "$BACKUP_DIR/supplement_tracker_$TIMESTAMP.sql"
    
    # Upload to S3
    aws s3 cp "$BACKUP_DIR/supplement_tracker_$TIMESTAMP.sql.gz" \
              "s3://supplement-tracker-backups/"

Disaster Recovery Plan

**Recovery Time Objectives (RTO)**:

- **Critical systems**: 1 hour
- **Non-critical systems**: 4 hours
- **Data recovery**: 15 minutes (point-in-time)

**Recovery Procedures**:

1. **Assess Impact**: Determine scope of disaster
2. **Activate DR Site**: Switch to backup infrastructure
3. **Restore Data**: Recover from latest backup
4. **Validate System**: Verify all services are operational
5. **Communicate Status**: Update users and stakeholders

Scaling and Performance
=======================
======================

Horizontal Pod Autoscaling

.. code-block:: yaml

    apiVersion: autoscaling/v2
    kind: HorizontalPodAutoscaler
    metadata:
      name: supplement-tracker-api-hpa
    spec:
      scaleTargetRef:
        apiVersion: apps/v1
        kind: Deployment
        name: supplement-tracker-api
      minReplicas: 3
      maxReplicas: 20
      metrics:
      - type: Resource
        resource:
          name: cpu
          target:
            type: Utilization
            averageUtilization: 70
      - type: Resource
        resource:
          name: memory
          target:
            type: Utilization
            averageUtilization: 80

Database Scaling

**Read Replicas**:

.. code-block:: text

    resource "aws_db_instance" "read_replica" {
      count = 2
      
      identifier = "supplement-tracker-read-${count.index + 1}"
      replicate_source_db = aws_db_instance.main.id
      
      instance_class = "db.t3.medium"
      publicly_accessible = false
      
      tags = {
        Name = "supplement-tracker-read-replica-${count.index + 1}"
      }
    }

**Connection Pooling**:

.. code-block:: python

    # SQLAlchemy connection pool configuration
    engine = create_async_engine(
        DATABASE_URL,
        pool_size=20,
        max_overflow=30,
        pool_pre_ping=True,
        pool_recycle=3600
