===============================================
Database Design
Database Design
===============================================

This document describes the database schema, relationships, and design decisions for the Supplement Tracker platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Database Overview
=================

The Supplement Tracker uses **PostgreSQL** as its primary database, chosen for:

- **ACID compliance** ensuring data integrity
- **JSONB support** for flexible health data storage
- **Advanced indexing** capabilities for performance
- **Robust backup and replication** features
- **Excellent Python ecosystem** integration

Schema Design Principles
========================

Naming Conventions
------------------

- **Tables**: Plural nouns in snake_case (e.g., ``users``, ``supplement_intakes``)
- **Columns**: snake_case with descriptive names
- **Primary Keys**: ``id`` (UUID type)
- **Foreign Keys**: ``{table_name}_id`` (e.g., ``user_id``)
- **Indexes**: ``ix_{table}_{column}`` for single column, ``ix_{table}_{col1}_{col2}`` for composite
- **Constraints**: ``ck_{table}_{constraint_name}`` for check constraints

Data Types
----------

- **Primary Keys**: UUID for global uniqueness and security
- **Timestamps**: ``TIMESTAMP WITH TIME ZONE`` for proper timezone handling
- **Text Fields**: ``VARCHAR`` with appropriate length limits
- **Numeric Data**: ``NUMERIC`` for precise decimal calculations
- **Boolean Fields**: ``BOOLEAN`` with explicit defaults
- **JSON Data**: ``JSONB`` for structured but flexible data

Core Tables
===========

Users Table
-----------

Stores user account information and authentication data.

.. code-block:: sql

    CREATE TABLE users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        username VARCHAR(50) UNIQUE NOT NULL,
        hashed_password VARCHAR(255) NOT NULL,
        full_name VARCHAR(255),
        bio TEXT,
        is_active BOOLEAN NOT NULL DEFAULT true,
        is_superuser BOOLEAN NOT NULL DEFAULT false,
        is_verified BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        last_login_at TIMESTAMP WITH TIME ZONE
    );

    -- Indexes
    CREATE INDEX ix_users_email ON users(email);
    CREATE INDEX ix_users_username ON users(username);
    CREATE INDEX ix_users_is_active ON users(is_active);
    CREATE INDEX ix_users_created_at ON users(created_at);

**Key Design Decisions**:

- UUID primary key for security and distributed system compatibility
- Separate ``is_active`` and ``is_verified`` flags for account management
- ``updated_at`` automatically updated via trigger
- Email and username uniqueness enforced at database level

User Sessions Table
-------------------

Tracks active user sessions for security and analytics.

.. code-block:: sql

    CREATE TABLE user_sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        ip_address INET,
        user_agent TEXT,
        is_active BOOLEAN NOT NULL DEFAULT true
    );

    -- Indexes
    CREATE INDEX ix_user_sessions_user_id ON user_sessions(user_id);
    CREATE INDEX ix_user_sessions_token ON user_sessions(session_token);
    CREATE INDEX ix_user_sessions_expires_at ON user_sessions(expires_at);

Supplements Table
-----------------

Central repository for supplement information.

.. code-block:: sql

    CREATE TABLE supplements (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        brand VARCHAR(255),
        description TEXT,
        category VARCHAR(100) NOT NULL,
        form VARCHAR(50) NOT NULL,
        serving_size NUMERIC(10,3),
        serving_unit VARCHAR(20),
        ingredients TEXT,
        barcode VARCHAR(50) UNIQUE,
        is_verified BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        created_by_user_id UUID
    );

    -- Indexes
    CREATE INDEX ix_supplements_name ON supplements(name);
    CREATE INDEX ix_supplements_brand ON supplements(brand);
    CREATE INDEX ix_supplements_category ON supplements(category);
    CREATE INDEX ix_supplements_barcode ON supplements(barcode);
    CREATE INDEX ix_supplements_is_verified ON supplements(is_verified);
    CREATE INDEX ix_supplements_created_by_user_id ON supplements(created_by_user_id);

    -- Full-text search index
    CREATE INDEX ix_supplements_search ON supplements 
    USING gin(to_tsvector('english', name || ' ' || coalesce(brand, '') || ' ' || coalesce(description, '')));

**Key Design Decisions**:

- ``NUMERIC`` type for serving_size to handle precise measurements
- Optional barcode field with uniqueness constraint
- Full-text search index for name, brand, and description
- ``is_verified`` flag for community moderation
- Reference to creating user for accountability

Supplement Intakes Table
-------------------------

Records individual supplement consumption events.

.. code-block:: sql

    CREATE TABLE supplement_intakes (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        supplement_id UUID NOT NULL REFERENCES supplements(id),
        dosage NUMERIC(10,3) NOT NULL,
        dosage_unit VARCHAR(20) NOT NULL,
        taken_at TIMESTAMP WITH TIME ZONE NOT NULL,
        notes TEXT,
        mood_before INTEGER CHECK (mood_before >= 1 AND mood_before <= 10),
        mood_after INTEGER CHECK (mood_after >= 1 AND mood_after <= 10),
        energy_before INTEGER CHECK (energy_before >= 1 AND energy_before <= 10),
        energy_after INTEGER CHECK (energy_after >= 1 AND energy_after <= 10),
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX ix_supplement_intakes_user_id ON supplement_intakes(user_id);
    CREATE INDEX ix_supplement_intakes_supplement_id ON supplement_intakes(supplement_id);
    CREATE INDEX ix_supplement_intakes_taken_at ON supplement_intakes(taken_at);
    CREATE INDEX ix_supplement_intakes_user_taken_at ON supplement_intakes(user_id, taken_at);

**Key Design Decisions**:

- Foreign key constraint to supplements table
- Check constraints for mood/energy ratings (1-10 scale)
- Separate ``taken_at`` timestamp for actual consumption time
- Composite index on user_id and taken_at for efficient user queries

Supplement Stacks Table
-----------------------

Manages user-defined supplement combinations.

.. code-block:: sql

    CREATE TABLE supplement_stacks (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        is_active BOOLEAN NOT NULL DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX ix_supplement_stacks_user_id ON supplement_stacks(user_id);
    CREATE INDEX ix_supplement_stacks_is_active ON supplement_stacks(is_active);

Supplement Stack Items Table
----------------------------

Links supplements to stacks with dosage information.

.. code-block:: sql

    CREATE TABLE supplement_stack_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        stack_id UUID NOT NULL REFERENCES supplement_stacks(id) ON DELETE CASCADE,
        supplement_id UUID NOT NULL REFERENCES supplements(id),
        dosage NUMERIC(10,3) NOT NULL,
        dosage_unit VARCHAR(20) NOT NULL,
        timing VARCHAR(50), -- e.g., "morning", "evening", "with_meal"
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
    );

    -- Indexes
    CREATE INDEX ix_supplement_stack_items_stack_id ON supplement_stack_items(stack_id);
    CREATE INDEX ix_supplement_stack_items_supplement_id ON supplement_stack_items(supplement_id);
    CREATE UNIQUE INDEX ix_supplement_stack_items_unique ON supplement_stack_items(stack_id, supplement_id);

Relationships and Constraints
=============================

Entity Relationships
--------------------

.. code-block:: text

    users ||--o{ user_sessions : has
    users ||--o{ supplement_intakes : logs
    users ||--o{ supplement_stacks : creates
    users ||--o{ supplements : creates
    
    supplements ||--o{ supplement_intakes : consumed_as
    supplements ||--o{ supplement_stack_items : included_in
    
    supplement_stacks ||--o{ supplement_stack_items : contains

Foreign Key Constraints
-----------------------

- ``supplement_intakes.supplement_id`` → ``supplements.id``
- ``supplement_stack_items.stack_id`` → ``supplement_stacks.id`` (CASCADE DELETE)
- ``supplement_stack_items.supplement_id`` → ``supplements.id``

**Note**: User references are not enforced as foreign keys to allow for user data deletion while preserving anonymized research data.

Check Constraints
-----------------

- Mood and energy ratings must be between 1 and 10
- Email format validation (handled at application level)
- Username format validation (handled at application level)

Data Integrity and Validation
=============================

Database-Level Validation
-------------------------

- **NOT NULL constraints** on required fields
- **UNIQUE constraints** on email, username, barcode
- **CHECK constraints** for rating scales
- **Foreign key constraints** for referential integrity

Application-Level Validation
----------------------------

- **Pydantic schemas** for input validation
- **Email format** validation
- **Username format** validation (alphanumeric, underscore, hyphen)
- **Password strength** requirements

Indexing Strategy
=================

Primary Indexes
---------------

- **Primary keys**: Automatic B-tree indexes on UUID columns
- **Unique constraints**: Automatic indexes on email, username, barcode

Query Optimization Indexes
--------------------------

- **User queries**: Indexes on user_id for all user-related tables
- **Time-based queries**: Indexes on timestamp columns
- **Search functionality**: Full-text search indexes
- **Composite indexes**: For common query patterns

Performance Considerations
==========================
=========================

Query Patterns
--------------

Common query patterns optimized with indexes:

1. **User supplement history**: ``user_id + taken_at``
2. **Supplement search**: Full-text search on name, brand, description
3. **Recent intakes**: ``user_id + taken_at DESC``
4. **Active stacks**: ``user_id + is_active``

Connection Pooling
------------------

- **SQLAlchemy async engine** with connection pooling
- **Pool size**: Configured based on expected concurrent users
- **Pool recycle**: 300 seconds to handle connection timeouts
- **Pool pre-ping**: Validates connections before use

Data Archival Strategy
======================

Historical Data
---------------

- **Intake records**: Retained indefinitely for research value
- **User sessions**: Cleaned up after expiration
- **Audit logs**: Retained per compliance requirements

Anonymization
-------------

For research purposes, user data can be anonymized:

- Replace user_id with anonymous research_id
- Remove personally identifiable information
- Maintain data relationships for analysis

Backup and Recovery
===================

Backup Strategy
---------------

- **Daily full backups** of the entire database
- **Continuous WAL archiving** for point-in-time recovery
- **Cross-region replication** for disaster recovery
- **Backup verification** with automated restore tests

Recovery Procedures
-------------------

- **Point-in-time recovery** for data corruption
- **Failover procedures** for primary database failure
- **Data validation** after recovery operations

Migration Management
====================

Alembic Integration
-------------------

Database schema changes are managed through Alembic:

- **Version control** for database schema
- **Automated migrations** in deployment pipeline
- **Rollback capabilities** for failed migrations
- **Data migration scripts** for complex changes

Migration Best Practices
------------------------

- **Backward compatibility** during rolling deployments
- **Data validation** before and after migrations
- **Performance testing** for large table alterations
- **Rollback procedures** for each migration

Future Considerations
=====================
====================

Scaling Strategies
------------------

- **Read replicas** for query distribution
- **Partitioning** for large tables (intakes by date)
- **Sharding** by user_id for horizontal scaling
- **Caching layer** for frequently accessed data

Additional Tables
-----------------

Planned tables for future features:

- **Community posts and discussions**
- **Peer review and ratings**
- **Research studies and protocols**
- **Expert validations and recommendations**
