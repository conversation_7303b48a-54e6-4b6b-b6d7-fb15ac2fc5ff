===============================================
Supplement Tracking Guide
===============================================

This guide covers how to effectively track supplements, log intakes, and manage your supplement regimen using the Supplement Tracker platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

The Supplement Tracker provides comprehensive tools for:

- **Supplement Database**: Browse and search thousands of supplements
- **Intake Logging**: Record when and how much you take
- **Stack Management**: Organize supplements into custom stacks
- **Progress Tracking**: Monitor mood, energy, and health metrics
- **Data Analysis**: Visualize patterns and correlations

Getting Started
===============

Adding Your First Supplement
----------------------------

**Step 1: Search the Database**

.. code-block:: bash

    curl -X GET "https://api.supplementtracker.com/api/v1/supplements?search=vitamin%20d" \
         -H "Authorization: Bearer YOUR_TOKEN"

**Step 2: Create New Supplement (if not found)**

.. code-block:: json

    {
        "name": "Vitamin D3",
        "brand": "Nature's Way",
        "category": "Vitamin",
        "form": "Capsule",
        "serving_size": 1,
        "serving_unit": "capsule",
        "ingredients": "Vitamin D3 (cholecalciferol) 5000 IU"
    }

**Step 3: Log Your First Intake**

.. code-block:: json

    {
        "supplement_id": "123e4567-e89b-12d3-a456-426614174000",
        "dosage": 5000,
        "dosage_unit": "IU",
        "taken_at": "2025-06-18T08:00:00Z",
        "notes": "Taken with breakfast",
        "mood_before": 7,
        "energy_before": 6
    }

Supplement Database
==================

Browsing Supplements
-------------------

**Search by Name or Brand**:

.. code-block:: bash

    GET /api/v1/supplements?search=magnesium&brand=thorne

**Filter by Category**:

.. code-block:: bash

    GET /api/v1/supplements?category=mineral

**Available Categories**:

- Vitamin
- Mineral
- Amino Acid
- Fatty Acid
- Herb
- Probiotic
- Enzyme
- Other

Adding New Supplements
---------------------

**Required Information**:

- Name
- Category
- Form (capsule, tablet, powder, liquid)

**Optional Information**:

- Brand
- Description
- Serving size and unit
- Ingredients list
- Barcode

**Verification Process**:

New supplements are marked as unverified until reviewed by moderators or verified users.

Intake Logging
==============

Recording Supplement Intakes
----------------------------

**Basic Intake Log**:

.. code-block:: python

    import asyncio
    from datetime import datetime, timezone

    async def log_supplement_intake(client, supplement_id, dosage, unit):
        intake_data = {
            "supplement_id": supplement_id,
            "dosage": dosage,
            "dosage_unit": unit,
            "taken_at": datetime.now(timezone.utc).isoformat()
        }
        
        response = await client.post("/intakes", json=intake_data)
        return response.json()

**Enhanced Intake Log with Tracking**:

.. code-block:: python

    async def log_detailed_intake(client, supplement_id, dosage, unit, **kwargs):
        intake_data = {
            "supplement_id": supplement_id,
            "dosage": dosage,
            "dosage_unit": unit,
            "taken_at": datetime.now(timezone.utc).isoformat(),
            "notes": kwargs.get("notes"),
            "mood_before": kwargs.get("mood_before"),
            "mood_after": kwargs.get("mood_after"),
            "energy_before": kwargs.get("energy_before"),
            "energy_after": kwargs.get("energy_after")
        }
        
        # Remove None values
        intake_data = {k: v for k, v in intake_data.items() if v is not None}
        
        response = await client.post("/intakes", json=intake_data)
        return response.json()

    # Usage
    await log_detailed_intake(
        client,
        supplement_id="123e4567-e89b-12d3-a456-426614174000",
        dosage=400,
        unit="mg",
        notes="Taken before bed for better sleep",
        mood_before=6,
        energy_before=4
    )

Mood and Energy Tracking
------------------------

**Rating Scale** (1-10):

- **1-3**: Poor/Low
- **4-6**: Average/Moderate  
- **7-8**: Good/High
- **9-10**: Excellent/Very High

**Best Practices**:

1. **Consistent timing**: Rate at the same times daily
2. **Honest assessment**: Use the full scale range
3. **Context notes**: Include relevant factors (sleep, stress, etc.)
4. **Regular updates**: Log both before and after ratings when possible

Supplement Stacks
=================

Creating Supplement Stacks
--------------------------

**What are Stacks?**

Supplement stacks are predefined combinations of supplements that you take together for specific health goals.

**Creating a Stack**:

.. code-block:: json

    {
        "name": "Morning Energy Stack",
        "description": "Supplements for sustained energy and focus",
        "is_active": true
    }

**Adding Supplements to Stack**:

.. code-block:: json

    {
        "stack_id": "stack_123",
        "supplement_id": "supp_456",
        "dosage": 200,
        "dosage_unit": "mg",
        "timing": "morning",
        "notes": "Take with food"
    }

**Example Stacks**:

1. **Morning Energy**:
   - B-Complex (1 capsule)
   - Vitamin D3 (5000 IU)
   - Magnesium (200mg)

2. **Evening Recovery**:
   - Magnesium Glycinate (400mg)
   - Melatonin (3mg)
   - Zinc (15mg)

3. **Workout Support**:
   - Creatine (5g)
   - Beta-Alanine (3g)
   - Citrulline (6g)

Managing Your Stacks
--------------------

**Activate/Deactivate Stacks**:

.. code-block:: python

    async def toggle_stack(client, stack_id, is_active):
        response = await client.put(
            f"/stacks/{stack_id}",
            json={"is_active": is_active}
        )
        return response.json()

**Log Entire Stack**:

.. code-block:: python

    async def log_stack_intake(client, stack_id):
        # Get stack details
        stack = await client.get(f"/stacks/{stack_id}")
        
        # Log each supplement in the stack
        intakes = []
        for item in stack["data"]["items"]:
            intake = await client.post("/intakes", json={
                "supplement_id": item["supplement_id"],
                "dosage": item["dosage"],
                "dosage_unit": item["dosage_unit"],
                "taken_at": datetime.now(timezone.utc).isoformat(),
                "notes": f"Part of {stack['data']['name']} stack"
            })
            intakes.append(intake)
        
        return intakes

Data Analysis and Insights
==========================

Viewing Your History
--------------------

**Get Recent Intakes**:

.. code-block:: bash

    GET /api/v1/intakes/history?limit=50

**Filter by Date Range**:

.. code-block:: bash

    GET /api/v1/intakes/history?start_date=2025-06-01&end_date=2025-06-18

**Filter by Supplement**:

.. code-block:: bash

    GET /api/v1/intakes/history?supplement_id=123e4567-e89b-12d3-a456-426614174000

Daily Summary
------------

**Get Today's Intakes**:

.. code-block:: bash

    GET /api/v1/intakes/daily

**Response Example**:

.. code-block:: json

    {
        "data": {
            "date": "2025-06-18",
            "total_intakes": 5,
            "supplements_taken": 3,
            "average_mood": 7.2,
            "average_energy": 6.8,
            "intakes": [
                {
                    "supplement": "Vitamin D3",
                    "dosage": 5000,
                    "unit": "IU",
                    "time": "08:00:00"
                }
            ]
        }
    }

Progress Tracking
-----------------

**Mood and Energy Trends**:

.. code-block:: python

    async def get_mood_energy_trends(client, days=30):
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        response = await client.get("/intakes/history", params={
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "limit": 1000
        })
        
        intakes = response.json()["data"]
        
        # Calculate daily averages
        daily_stats = {}
        for intake in intakes:
            date = intake["taken_at"][:10]  # Extract date
            if date not in daily_stats:
                daily_stats[date] = {"mood": [], "energy": []}
            
            if intake.get("mood_before"):
                daily_stats[date]["mood"].append(intake["mood_before"])
            if intake.get("energy_before"):
                daily_stats[date]["energy"].append(intake["energy_before"])
        
        # Calculate averages
        trends = {}
        for date, stats in daily_stats.items():
            trends[date] = {
                "avg_mood": sum(stats["mood"]) / len(stats["mood"]) if stats["mood"] else None,
                "avg_energy": sum(stats["energy"]) / len(stats["energy"]) if stats["energy"] else None
            }
        
        return trends

Best Practices
==============

Consistent Tracking
-------------------

1. **Set reminders**: Use phone alarms or app notifications
2. **Track immediately**: Log intakes right after taking supplements
3. **Be honest**: Record actual dosages and timing
4. **Include context**: Note relevant factors (food, sleep, stress)

Dosage Management
----------------

1. **Start low**: Begin with recommended dosages
2. **Adjust gradually**: Make small changes over time
3. **Monitor effects**: Track mood, energy, and any side effects
4. **Consult professionals**: Work with healthcare providers

Data Quality
-----------

1. **Accurate timing**: Record actual intake times
2. **Precise dosages**: Use measuring tools when needed
3. **Consistent units**: Stick to standard units (mg, IU, etc.)
4. **Regular updates**: Keep supplement information current

Safety Considerations
====================

Supplement Interactions
----------------------

**Common Interactions**:

- Iron and calcium (reduce absorption)
- Fat-soluble vitamins (A, D, E, K) with fats
- Magnesium and zinc (compete for absorption)

**Monitoring Guidelines**:

1. **Start one at a time**: Introduce new supplements individually
2. **Watch for side effects**: Monitor for adverse reactions
3. **Check with medications**: Consult healthcare providers
4. **Regular blood work**: Monitor relevant biomarkers

Dosage Safety
------------

**General Guidelines**:

- Follow manufacturer recommendations
- Don't exceed upper limits
- Consider cumulative intake from all sources
- Adjust for body weight and health status

**Red Flags**:

- Unusual side effects
- Digestive issues
- Changes in sleep patterns
- Mood changes

Mobile App Integration
=====================

Planned Features
---------------

**Mobile App** (Coming Soon):

- Barcode scanning for easy supplement addition
- Push notifications for intake reminders
- Offline logging with sync
- Photo logging for supplement bottles
- Quick stack logging
- Health metric integration (Apple Health, Google Fit)

**Current Workarounds**:

- Use mobile browser for web interface
- Create shortcuts to common API endpoints
- Use third-party apps with webhook integration

API Integration
==============

Custom Applications
------------------

**Building Your Own Tracker**:

.. code-block:: python

    class PersonalSupplementTracker:
        def __init__(self, api_client):
            self.client = api_client
            self.daily_stack = []

        async def setup_daily_routine(self, stack_id):
            """Set up daily supplement routine"""
            stack = await self.client.get(f"/stacks/{stack_id}")
            self.daily_stack = stack["data"]["items"]

        async def log_morning_routine(self):
            """Log all morning supplements"""
            morning_supplements = [
                item for item in self.daily_stack 
                if item.get("timing") == "morning"
            ]
            
            for supplement in morning_supplements:
                await self.client.post("/intakes", json={
                    "supplement_id": supplement["supplement_id"],
                    "dosage": supplement["dosage"],
                    "dosage_unit": supplement["dosage_unit"],
                    "taken_at": datetime.now(timezone.utc).isoformat(),
                    "notes": "Morning routine"
                })

        async def get_weekly_summary(self):
            """Get summary of the past week"""
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            response = await self.client.get("/intakes/history", params={
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            })
            
            return self.analyze_weekly_data(response.json()["data"])

**Third-party Integrations**:

- Fitness trackers
- Health monitoring apps
- Nutrition tracking apps
- Electronic health records

Troubleshooting
==============

Common Issues
------------

**Supplement Not Found**:

1. Search with different terms
2. Check spelling and brand name
3. Create new supplement entry
4. Contact support for verification

**Intake Logging Errors**:

1. Verify supplement ID exists
2. Check dosage format and units
3. Ensure proper authentication
4. Validate timestamp format

**Data Sync Issues**:

1. Check internet connection
2. Verify API credentials
3. Review rate limiting
4. Check for service status

Getting Help
-----------

**Documentation**:

- API Reference: :doc:`../api/index`
- Authentication Guide: :doc:`authentication`
- Configuration Guide: :doc:`configuration`

**Support Channels**:

- GitHub Issues: Report bugs and feature requests
- Community Forum: Get help from other users
- Email Support: Direct assistance for account issues

**Contributing**:

- Submit supplement data corrections
- Report missing supplements
- Suggest new features
- Contribute to open source development

Next Steps
==========

Now that you understand supplement tracking:

1. **Explore community features**: :doc:`community-features`
2. **Set up data exports**: :doc:`../api/research`
3. **Review API documentation**: :doc:`../api/index`
4. **Join the community**: :doc:`../developer/contributing`

For advanced usage:

- :doc:`../api/supplements`
- :doc:`../api/schemas`
- :doc:`../architecture/database-design`
