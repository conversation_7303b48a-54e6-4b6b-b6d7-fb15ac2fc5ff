===============================================
Installation Guide
Installation Guide
===============================================

This guide will help you install and set up the Supplement Tracker platform for development or production use.

.. contents:: Table of Contents
   :local:
   :depth: 2

Prerequisites
=============

System Requirements
-------------------

**Minimum Requirements**:

- **Operating System**: Linux (Ubuntu 20.04+), macOS (10.15+), or Windows 10+ with WSL2
- **Memory**: 4GB RAM (8GB recommended)
- **Storage**: 10GB free disk space
- **Network**: Internet connection for package downloads

**Software Dependencies**:

- **Nix Package Manager** (recommended) or:
  - Python 3.11+
  - PostgreSQL 13+
  - Redis 6+
  - Git

Development Environment Setup
=============================
============================

Option 1: Using Nix (Recommended)
---------------------------------

Nix provides a reproducible development environment with all dependencies managed automatically.

**Install Nix**:

.. code-block:: bash

    # Install Nix (single-user installation)
    curl -L https://nixos.org/nix/install | sh

    # Reload your shell
    source ~/.bashrc

    # Enable flakes (optional but recommended)
    mkdir -p ~/.config/nix
    echo "experimental-features = nix-command flakes" >> ~/.config/nix/nix.conf

**Clone and Setup**:

.. code-block:: bash

    # Clone the repository
    git clone https://github.com/forkrul/day2-supplement-tracker.git
    cd day2-supplement-tracker

    # Enter the development environment
    nix-shell

    # The environment will automatically install all dependencies
    # You should see a welcome message with available commands

**Verify Installation**:

.. code-block:: bash

    # Check Python version
    python --version  # Should show Python 3.11.x

    # Check available commands
    make help

Option 2: Manual Installation
-----------------------------

If you prefer not to use Nix, you can install dependencies manually.

**Install Python 3.11+**:

.. code-block:: bash

    # Ubuntu/Debian
    sudo apt update
    sudo apt install python3.11 python3.11-venv python3.11-dev

    # macOS (using Homebrew)
    brew install python@3.11

    # Windows (using Chocolatey)
    choco install python --version=3.11.0

**Install PostgreSQL**:

.. code-block:: bash

    # Ubuntu/Debian
    sudo apt install postgresql postgresql-contrib

    # macOS
    brew install postgresql
    brew services start postgresql

    # Windows
    # Download from https://www.postgresql.org/download/windows/

**Install Redis**:

.. code-block:: bash

    # Ubuntu/Debian
    sudo apt install redis-server

    # macOS
    brew install redis
    brew services start redis

    # Windows
    # Use Redis for Windows or WSL2

**Setup Python Environment**:

.. code-block:: bash

    # Clone the repository
    git clone https://github.com/forkrul/day2-supplement-tracker.git
    cd day2-supplement-tracker

    # Create virtual environment
    python3.11 -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate

    # Install dependencies
    pip install -r requirements.txt
    pip install -r requirements-dev.txt

Database Setup
==============

PostgreSQL Configuration
------------------------

**Create Database and User**:

.. code-block:: bash

    # Connect to PostgreSQL as superuser
    sudo -u postgres psql

.. code-block:: sql

    -- Create database
    CREATE DATABASE supplement_tracker;

    -- Create user
    CREATE USER supplement_user WITH PASSWORD 'your_secure_password';

    -- Grant privileges
    GRANT ALL PRIVILEGES ON DATABASE supplement_tracker TO supplement_user;

    -- Exit PostgreSQL
    \q

**Configure Connection**:

Create a ``.env`` file in the project root:

.. code-block:: bash

    # Copy the example environment file
    cp .env.example .env

Edit the ``.env`` file with your database credentials:

.. code-block:: bash

    # Database Configuration
    DATABASE_URL=postgresql://supplement_user:your_secure_password@localhost:5432/supplement_tracker

    # Redis Configuration
    REDIS_URL=redis://localhost:6379/0

    # Security
    SECRET_KEY=your-super-secret-key-change-this-in-production
    ACCESS_TOKEN_EXPIRE_MINUTES=15

    # Environment
    DEBUG=true
    TESTING=false

Database Migration
------------------
-----------------

**Initialize Database Schema**:

.. code-block:: bash

    # Run database migrations
    alembic upgrade head

    # Verify tables were created
    psql -h localhost -U supplement_user -d supplement_tracker -c "\dt"

**Create Initial Superuser** (Optional):

.. code-block:: bash

    # Using the provided script
    python scripts/create_superuser.py

    # Or manually through the API after starting the server

Application Configuration
=========================

Environment Variables
---------------------
--------------------

**Required Variables**:

.. list-table::
   :header-rows: 1
   :widths: 30 70

   * - Variable
     - Description
   * - DATABASE_URL
     - PostgreSQL connection string
   * - REDIS_URL
     - Redis connection string
   * - SECRET_KEY
     - JWT signing secret (generate with ``openssl rand -hex 32``)

**Optional Variables**:

.. list-table::
   :header-rows: 1
   :widths: 30 50 20

   * - Variable
     - Description
     - Default
   * - DEBUG
     - Enable debug mode
     - false
   * - ACCESS_TOKEN_EXPIRE_MINUTES
     - JWT token expiration
     - 15
   * - CORS_ORIGINS
     - Allowed CORS origins
     - []
   * - SENTRY_DSN
     - Error tracking URL
     - None

**Production Variables**:

.. code-block:: bash

    # Production environment
    DEBUG=false
    TESTING=false

    # Security
    SECRET_KEY=your-production-secret-key
    CORS_ORIGINS=["https://yourdomain.com"]

    # Database (use connection pooling)
    DATABASE_URL=***********************************/supplement_tracker?pool_size=20&max_overflow=30

    # Monitoring
    SENTRY_DSN=https://<EMAIL>/project-id

Configuration Files
-------------------
------------------

**Alembic Configuration** (``alembic.ini``):

.. code-block:: ini

    [alembic]
    script_location = alembic
    sqlalchemy.url = postgresql://supplement_user:password@localhost:5432/supplement_tracker

    [loggers]
    keys = root,sqlalchemy,alembic

    [handlers]
    keys = console

    [formatters]
    keys = generic

**Logging Configuration**:

Create ``logging.conf``:

.. code-block:: ini

    [loggers]
    keys=root,app

    [handlers]
    keys=consoleHandler,fileHandler

    [formatters]
    keys=simpleFormatter,jsonFormatter

    [logger_root]
    level=INFO
    handlers=consoleHandler

    [logger_app]
    level=DEBUG
    handlers=consoleHandler,fileHandler
    qualname=app
    propagate=0

    [handler_consoleHandler]
    class=StreamHandler
    level=DEBUG
    formatter=jsonFormatter
    args=(sys.stdout,)

    [handler_fileHandler]
    class=FileHandler
    level=INFO
    formatter=jsonFormatter
    args=('logs/app.log',)

    [formatter_simpleFormatter]
    format=%(asctime)s - %(name)s - %(levelname)s - %(message)s

    [formatter_jsonFormatter]
    class=pythonjsonlogger.jsonlogger.JsonFormatter
    format=%(asctime)s %(name)s %(levelname)s %(message)s

Running the Application
=======================
======================

Development Server
------------------
-----------------

**Using Make Commands**:

.. code-block:: bash

    # Start development server
    make dev

    # Run with auto-reload
    make dev-reload

    # Run tests
    make test

    # Format code
    make format

    # Run linting
    make lint

**Manual Startup**:

.. code-block:: bash

    # Activate environment (if not using Nix)
    source venv/bin/activate

    # Start the server
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

    # Or using Python directly
    python -m app.main

**Verify Installation**:

Open your browser and navigate to:

- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

Production Deployment
=====================
====================

Docker Deployment
-----------------

**Build Docker Image**:

.. code-block:: bash

    # Build the image
    docker build -t supplement-tracker:latest .

    # Run with Docker Compose
    docker-compose up -d

**Docker Compose Configuration**:

.. code-block:: yaml

    version: '3.8'
    services:
      app:
        build: .
        ports:
          - "8000:8000"
        environment:
          - DATABASE_URL=******************************/supplement_tracker
          - REDIS_URL=redis://redis:6379/0
          - SECRET_KEY=${SECRET_KEY}
        depends_on:
          - db
          - redis
        restart: unless-stopped

      db:
        image: postgres:15
        environment:
          POSTGRES_DB: supplement_tracker
          POSTGRES_USER: user
          POSTGRES_PASSWORD: pass
        volumes:
          - postgres_data:/var/lib/postgresql/data
        restart: unless-stopped

      redis:
        image: redis:7-alpine
        volumes:
          - redis_data:/data
        restart: unless-stopped

      nginx:
        image: nginx:alpine
        ports:
          - "80:80"
          - "443:443"
        volumes:
          - ./nginx.conf:/etc/nginx/nginx.conf
          - ./ssl:/etc/nginx/ssl
        depends_on:
          - app
        restart: unless-stopped

    volumes:
      postgres_data:
      redis_data:

Kubernetes Deployment
---------------------

**Basic Deployment**:

.. code-block:: yaml

    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: supplement-tracker
    spec:
      replicas: 3
      selector:
        matchLabels:
          app: supplement-tracker
      template:
        metadata:
          labels:
            app: supplement-tracker
        spec:
          containers:
          - name: app
            image: supplement-tracker:latest
            ports:
            - containerPort: 8000
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: database-url
            - name: SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: secret-key

**Service Configuration**:

.. code-block:: yaml

    apiVersion: v1
    kind: Service
    metadata:
      name: supplement-tracker-service
    spec:
      selector:
        app: supplement-tracker
      ports:
      - protocol: TCP
        port: 80
        targetPort: 8000
      type: LoadBalancer

Troubleshooting
===============

Common Issues
-------------
------------

**Database Connection Issues**:

.. code-block:: bash

    # Test database connection
    psql -h localhost -U supplement_user -d supplement_tracker -c "SELECT version();"

    # Check if PostgreSQL is running
    sudo systemctl status postgresql

    # Check database logs
    sudo tail -f /var/log/postgresql/postgresql-*.log

**Redis Connection Issues**:

.. code-block:: bash

    # Test Redis connection
    redis-cli ping

    # Check if Redis is running
    sudo systemctl status redis

    # Check Redis logs
    sudo tail -f /var/log/redis/redis-server.log

**Python Import Errors**:

.. code-block:: bash

    # Check Python path
    python -c "import sys; print(sys.path)"

    # Verify virtual environment
    which python
    which pip

    # Reinstall dependencies
    pip install -r requirements.txt --force-reinstall

**Port Already in Use**:

.. code-block:: bash

    # Find process using port 8000
    lsof -i :8000

    # Kill the process
    kill -9 <PID>

    # Or use a different port
    uvicorn app.main:app --port 8001

Performance Tuning
==================

Database Optimization
---------------------

**PostgreSQL Configuration** (``postgresql.conf``):

.. code-block:: ini

    # Memory settings
    shared_buffers = 256MB
    effective_cache_size = 1GB
    work_mem = 4MB

    # Connection settings
    max_connections = 100

    # Logging
    log_statement = 'all'
    log_duration = on
    log_min_duration_statement = 1000

**Connection Pooling**:

.. code-block:: python

    # SQLAlchemy engine configuration
    engine = create_async_engine(
        DATABASE_URL,
        pool_size=20,
        max_overflow=30,
        pool_pre_ping=True,
        pool_recycle=3600
    )

Application Optimization
------------------------
-----------------------

**Uvicorn Configuration**:

.. code-block:: bash

    # Production settings
    uvicorn app.main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers 4 \
        --worker-class uvicorn.workers.UvicornWorker \
        --access-log \
        --log-level info

**Environment Variables**:

.. code-block:: bash

    # Python optimization
    PYTHONOPTIMIZE=1
    PYTHONDONTWRITEBYTECODE=1

    # Async settings
    UVLOOP_ENABLED=1

Security Hardening
==================

Production Security
-------------------

**Environment Security**:

.. code-block:: bash

    # Generate secure secret key
    openssl rand -hex 32

    # Set secure file permissions
    chmod 600 .env
    chown app:app .env

**Database Security**:

.. code-block:: sql

    -- Create restricted user for application
    CREATE USER app_user WITH PASSWORD 'secure_password';
    GRANT CONNECT ON DATABASE supplement_tracker TO app_user;
    GRANT USAGE ON SCHEMA public TO app_user;
    GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;

**Firewall Configuration**:

.. code-block:: bash

    # Ubuntu UFW
    sudo ufw allow 22/tcp    # SSH
    sudo ufw allow 80/tcp    # HTTP
    sudo ufw allow 443/tcp   # HTTPS
    sudo ufw deny 8000/tcp   # Block direct app access
    sudo ufw enable

SSL/TLS Setup
-------------
------------

**Let's Encrypt with Certbot**:

.. code-block:: bash

    # Install Certbot
    sudo apt install certbot python3-certbot-nginx

    # Obtain certificate
    sudo certbot --nginx -d yourdomain.com

    # Auto-renewal
    sudo crontab -e
    # Add: 0 12 * * * /usr/bin/certbot renew --quiet

Next Steps
==========

After successful installation:

1. **Read the Configuration Guide**: :doc:`configuration`
2. **Explore the API**: :doc:`api-usage`
3. **Set up Authentication**: :doc:`authentication`
4. **Start Tracking Supplements**: :doc:`supplement-tracking`

For production deployments, also review:

- :doc:`../architecture/security`
- :doc:`../architecture/deployment`
