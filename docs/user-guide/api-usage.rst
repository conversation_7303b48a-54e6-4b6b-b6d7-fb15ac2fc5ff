===============
API Usage Guide
===============

This guide provides comprehensive examples and best practices for using the Supplement Tracker API.

.. contents:: Table of Contents
   :local:
   :depth: 2

Getting Started
===============

API Overview

The Supplement Tracker API is a RESTful API that provides:

- **User management**: Registration, authentication, profile management
- **Supplement tracking**: CRUD operations for supplements and intake logging
- **Community features**: Social interactions and peer review (planned)
- **Research tools**: Data analysis and export capabilities (planned)

**Base URL**: ``https://api.supplementtracker.com/api/v1``

**Authentication**: JWT Bearer tokens

**Content Type**: ``application/json``

Quick Start Example

.. code-block:: bash

    # 1. Register a new user
    curl -X POST "https://api.supplementtracker.com/api/v1/auth/register" \
         -H "Content-Type: application/json" \
         -d '{
           "email": "<EMAIL>",
           "username": "testuser",
           "password": "SecurePass123!",
           "full_name": "Test User"
         }'

    # 2. Login to get access token
    curl -X POST "https://api.supplementtracker.com/api/v1/auth/login" \
         -H "Content-Type: application/json" \
         -d '{
           "username": "testuser",
           "password": "SecurePass123!"
         }'

    # 3. Use the token for authenticated requests
    curl -X GET "https://api.supplementtracker.com/api/v1/users/me" \
         -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

Interactive Documentation

The API provides interactive documentation:

- **Swagger UI**: ``https://api.supplementtracker.com/docs``
- **ReDoc**: ``https://api.supplementtracker.com/redoc``
- **OpenAPI Spec**: ``https://api.supplementtracker.com/openapi.json``

Authentication
==============

User Registration

**Endpoint**: ``POST /api/v1/auth/register``

**Request**:

.. code-block:: json

    {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "SecurePass123!",
        "full_name": "Test User",
        "bio": "Health enthusiast and supplement researcher"
    }

**Response**:

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "username": "testuser",
            "full_name": "Test User",
            "bio": "Health enthusiast and supplement researcher",
            "is_active": true,
            "is_verified": false,
            "created_at": "2025-06-18T15:30:00Z",
            "updated_at": "2025-06-18T15:30:00Z",
            "last_login_at": null
        },
        "meta": {
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

**Python Example**:

.. code-block:: python

    import httpx
    import asyncio

    async def register_user():
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.supplementtracker.com/api/v1/auth/register",
                json={
                    "email": "<EMAIL>",
                    "username": "testuser",
                    "password": "SecurePass123!",
                    "full_name": "Test User"
                }
            )
            return response.json()

    # Run the registration
    result = asyncio.run(register_user())
    print(result)

User Login

**Endpoint**: ``POST /api/v1/auth/login``

**Request**:

.. code-block:: json

    {
        "username": "testuser",
        "password": "SecurePass123!"
    }

**Response**:

.. code-block:: json

    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer",
        "expires_in": 900,
        "user": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "username": "testuser",
            "email": "<EMAIL>"
        }
    }

**JavaScript Example**:

.. code-block:: javascript

    async function loginUser(username, password) {
        const response = await fetch('https://api.supplementtracker.com/api/v1/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        if (response.ok) {
            const data = await response.json();
            // Store the token for future requests
            localStorage.setItem('access_token', data.access_token);
            localStorage.setItem('refresh_token', data.refresh_token);
            return data;
        } else {
            throw new Error('Login failed');
        }
    }

    // Usage
    loginUser('testuser', 'SecurePass123!')
        .then(data => console.log('Login successful:', data))
        .catch(error => console.error('Login error:', error));

Token Refresh

**Endpoint**: ``POST /api/v1/auth/refresh``

**Request**:

.. code-block:: json

    {
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }

**Response**:

.. code-block:: json

    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer",
        "expires_in": 900
    }

User Management
===============

Get Current User

**Endpoint**: ``GET /api/v1/users/me``

**Headers**: ``Authorization: Bearer YOUR_ACCESS_TOKEN``

**Response**:

.. code-block:: json

    {
        "data": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "email": "<EMAIL>",
            "username": "testuser",
            "full_name": "Test User",
            "bio": "Health enthusiast and supplement researcher",
            "is_active": true,
            "is_verified": false,
            "created_at": "2025-06-18T15:30:00Z",
            "updated_at": "2025-06-18T15:30:00Z",
            "last_login_at": "2025-06-18T15:30:00Z"
        }
    }

Update User Profile

**Endpoint**: ``PUT /api/v1/users/me``

**Request**:

.. code-block:: json

    {
        "full_name": "Updated Full Name",
        "bio": "Updated bio with more information about my health journey"
    }

**Response**: Updated user object

**Python Example with Authentication**:

.. code-block:: python

    import httpx

    class SupplementTrackerClient:
        def __init__(self, base_url: str, access_token: str = None):
            self.base_url = base_url
            self.access_token = access_token
            self.client = httpx.AsyncClient()

        def set_token(self, token: str):
            self.access_token = token

        @property
        def headers(self):
            headers = {"Content-Type": "application/json"}
            if self.access_token:
                headers["Authorization"] = f"Bearer {self.access_token}"
            return headers

        async def get_current_user(self):
            response = await self.client.get(
                f"{self.base_url}/users/me",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

        async def update_profile(self, **kwargs):
            response = await self.client.put(
                f"{self.base_url}/users/me",
                json=kwargs,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()

    # Usage
    client = SupplementTrackerClient("https://api.supplementtracker.com/api/v1")
    client.set_token("your_access_token")

    # Get current user
    user = await client.get_current_user()
    print(f"Current user: {user['data']['username']}")

    # Update profile
    updated_user = await client.update_profile(
        full_name="New Name",
        bio="Updated bio"
    )

Supplement Management
=====================
====================

List Supplements

**Endpoint**: ``GET /api/v1/supplements``

**Query Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Type
     - Description
   * - search
     - string
     - Search term for name, brand, or description
   * - category
     - string
     - Filter by supplement category
   * - skip
     - integer
     - Number of records to skip (pagination)
   * - limit
     - integer
     - Maximum number of records to return (max 100)

**Example Request**:

.. code-block:: bash

    curl -X GET "https://api.supplementtracker.com/api/v1/supplements?search=vitamin&category=vitamin&limit=10" \
         -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

**Response**:

.. code-block:: json

    {
        "data": [
            {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "Vitamin D3",
                "brand": "Nature's Way",
                "description": "High-potency vitamin D3 for bone health",
                "category": "Vitamin",
                "form": "Capsule",
                "serving_size": 1,
                "serving_unit": "capsule",
                "ingredients": "Vitamin D3 (as cholecalciferol) 5000 IU",
                "barcode": "123456789012",
                "is_verified": true,
                "created_at": "2025-06-18T15:30:00Z",
                "updated_at": "2025-06-18T15:30:00Z"
            }
        ],
        "pagination": {
            "total": 150,
            "page": 1,
            "per_page": 10,
            "pages": 15
        }
    }

Create Supplement

**Endpoint**: ``POST /api/v1/supplements``

**Request**:

.. code-block:: json

    {
        "name": "Magnesium Glycinate",
        "brand": "Thorne",
        "description": "Highly absorbable form of magnesium",
        "category": "Mineral",
        "form": "Capsule",
        "serving_size": 2,
        "serving_unit": "capsules",
        "ingredients": "Magnesium (as magnesium glycinate) 200mg",
        "barcode": "987654321098"
    }

**Response**: Created supplement object

**JavaScript Example**:

.. code-block:: javascript

    class SupplementAPI {
        constructor(baseUrl, accessToken) {
            this.baseUrl = baseUrl;
            this.accessToken = accessToken;
        }

        async request(endpoint, options = {}) {
            const url = `${this.baseUrl}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.accessToken}`,
                    ...options.headers
                },
                ...options
            };

            const response = await fetch(url, config);
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error?.message || 'API request failed');
            }

            return response.json();
        }

        async getSupplements(params = {}) {
            const queryString = new URLSearchParams(params).toString();
            const endpoint = `/supplements${queryString ? '?' + queryString : ''}`;
            return this.request(endpoint);
        }

        async createSupplement(supplementData) {
            return this.request('/supplements', {
                method: 'POST',
                body: JSON.stringify(supplementData)
            });
        }

        async getSupplement(id) {
            return this.request(`/supplements/${id}`);
        }

        async updateSupplement(id, updateData) {
            return this.request(`/supplements/${id}`, {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
        }
    }

    // Usage
    const api = new SupplementAPI('https://api.supplementtracker.com/api/v1', 'your_token');

    // Search for supplements
    const supplements = await api.getSupplements({
        search: 'vitamin',
        category: 'vitamin',
        limit: 10
    });

    // Create a new supplement
    const newSupplement = await api.createSupplement({
        name: 'Omega-3 Fish Oil',
        brand: 'Nordic Naturals',
        category: 'Fatty Acid',
        form: 'Softgel'
    });

Supplement Intake Tracking
==========================
=========================

Log Supplement Intake

**Endpoint**: ``POST /api/v1/intakes``

**Request**:

.. code-block:: json

    {
        "supplement_id": "123e4567-e89b-12d3-a456-426614174000",
        "dosage": 5000,
        "dosage_unit": "IU",
        "taken_at": "2025-06-18T08:00:00Z",
        "notes": "Taken with breakfast for better absorption",
        "mood_before": 7,
        "mood_after": 8,
        "energy_before": 6,
        "energy_after": 7
    }

**Response**:

.. code-block:: json

    {
        "data": {
            "id": "456e7890-e89b-12d3-a456-426614174000",
            "user_id": "123e4567-e89b-12d3-a456-426614174000",
            "supplement_id": "123e4567-e89b-12d3-a456-426614174000",
            "dosage": 5000,
            "dosage_unit": "IU",
            "taken_at": "2025-06-18T08:00:00Z",
            "notes": "Taken with breakfast for better absorption",
            "mood_before": 7,
            "mood_after": 8,
            "energy_before": 6,
            "energy_after": 7,
            "created_at": "2025-06-18T15:30:00Z",
            "supplement": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "Vitamin D3",
                "brand": "Nature's Way"
            }
        }
    }

Get Intake History

**Endpoint**: ``GET /api/v1/intakes/history``

**Query Parameters**:

.. list-table::
   :header-rows: 1
   :widths: 20 20 60

   * - Parameter
     - Type
     - Description
   * - start_date
     - string
     - Start date (ISO format)
   * - end_date
     - string
     - End date (ISO format)
   * - supplement_id
     - string
     - Filter by specific supplement
   * - limit
     - integer
     - Maximum number of records

**Example**:

.. code-block:: bash

    curl -X GET "https://api.supplementtracker.com/api/v1/intakes/history?start_date=2025-06-01&end_date=2025-06-18&limit=50" \
         -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

**Python Example for Intake Tracking**:

.. code-block:: python

    from datetime import datetime, timezone
    import asyncio

    class IntakeTracker:
        def __init__(self, client):
            self.client = client

        async def log_intake(self, supplement_id: str, dosage: float, 
                           dosage_unit: str, **kwargs):
            intake_data = {
                "supplement_id": supplement_id,
                "dosage": dosage,
                "dosage_unit": dosage_unit,
                "taken_at": datetime.now(timezone.utc).isoformat(),
                **kwargs
            }
            
            response = await self.client.post("/intakes", json=intake_data)
            return response.json()

        async def get_daily_intakes(self, date: str = None):
            if not date:
                date = datetime.now().strftime("%Y-%m-%d")
            
            response = await self.client.get(f"/intakes/daily?date={date}")
            return response.json()

        async def get_intake_history(self, days: int = 30):
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            params = {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "limit": 100
            }
            
            response = await self.client.get("/intakes/history", params=params)
            return response.json()

    # Usage
    tracker = IntakeTracker(client)

    # Log a supplement intake
    intake = await tracker.log_intake(
        supplement_id="123e4567-e89b-12d3-a456-426614174000",
        dosage=5000,
        dosage_unit="IU",
        notes="Morning dose with breakfast",
        mood_before=7,
        energy_before=6
    )

    # Get today's intakes
    daily_intakes = await tracker.get_daily_intakes()

    # Get intake history for the last 30 days
    history = await tracker.get_intake_history(days=30)

Error Handling
==============

Error Response Format

All API errors follow a consistent format:

.. code-block:: json

    {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Invalid input data",
            "details": {
                "field": "email",
                "issue": "Invalid email format"
            },
            "timestamp": "2025-06-18T15:30:00Z",
            "request_id": "req_123456789"
        }
    }

Common Error Codes

.. list-table::
   :header-rows: 1
   :widths: 25 15 60

   * - Error Code
     - HTTP Status
     - Description
   * - VALIDATION_ERROR
     - 400
     - Request data validation failed
   * - AUTHENTICATION_REQUIRED
     - 401
     - Authentication credentials required
   * - AUTHENTICATION_FAILED
     - 401
     - Invalid authentication credentials
   * - AUTHORIZATION_FAILED
     - 403
     - Insufficient permissions
   * - RESOURCE_NOT_FOUND
     - 404
     - Requested resource does not exist
   * - RESOURCE_CONFLICT
     - 409
     - Resource already exists or conflict
   * - RATE_LIMIT_EXCEEDED
     - 429
     - Too many requests
   * - INTERNAL_SERVER_ERROR
     - 500
     - Unexpected server error

**Python Error Handling Example**:

.. code-block:: python

    import httpx

    class APIError(Exception):
        def __init__(self, error_data):
            self.code = error_data.get('code')
            self.message = error_data.get('message')
            self.details = error_data.get('details')
            super().__init__(self.message)

    class SupplementTrackerClient:
        async def request(self, method: str, endpoint: str, **kwargs):
            try:
                response = await self.client.request(
                    method, f"{self.base_url}{endpoint}", **kwargs
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError as e:
                if e.response.headers.get('content-type') == 'application/json':
                    error_data = e.response.json().get('error', {})
                    raise APIError(error_data)
                else:
                    raise APIError({
                        'code': 'HTTP_ERROR',
                        'message': f'HTTP {e.response.status_code}: {e.response.text}'
                    })

    # Usage with error handling
    try:
        user = await client.get_current_user()
    except APIError as e:
        if e.code == 'AUTHENTICATION_REQUIRED':
            print("Please log in first")
        elif e.code == 'RATE_LIMIT_EXCEEDED':
            print("Too many requests, please wait")
        else:
            print(f"API Error: {e.message}")

Rate Limiting
=============

Rate Limit Headers

The API includes rate limiting information in response headers:

.. code-block:: text

    X-RateLimit-Limit: 1000
    X-RateLimit-Remaining: 999
    X-RateLimit-Reset: 1640995200

**Rate Limits by User Type**:

.. list-table::
   :header-rows: 1
   :widths: 25 75

   * - User Type
     - Limit
   * - Anonymous
     - 100 requests per hour
   * - Authenticated
     - 1,000 requests per hour
   * - Premium
     - 5,000 requests per hour
   * - API Key
     - 10,000 requests per hour

**Handling Rate Limits**:

.. code-block:: python

    import asyncio
    from datetime import datetime

    class RateLimitedClient:
        def __init__(self, client):
            self.client = client

        async def request_with_retry(self, method: str, endpoint: str, **kwargs):
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    response = await self.client.request(method, endpoint, **kwargs)
                    return response
                except APIError as e:
                    if e.code == 'RATE_LIMIT_EXCEEDED' and retry_count < max_retries - 1:
                        # Extract retry-after from headers or use exponential backoff
                        wait_time = 2 ** retry_count
                        print(f"Rate limited, waiting {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        retry_count += 1
                    else:
                        raise

Best Practices
==============

Authentication Best Practices

1. **Store tokens securely**: Use secure storage mechanisms
2. **Handle token expiration**: Implement automatic token refresh
3. **Use HTTPS only**: Never send tokens over HTTP
4. **Implement logout**: Clear tokens on logout

**Token Management Example**:

.. code-block:: javascript

    class TokenManager {
        constructor() {
            this.accessToken = localStorage.getItem('access_token');
            this.refreshToken = localStorage.getItem('refresh_token');
        }

        setTokens(accessToken, refreshToken) {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            localStorage.setItem('access_token', accessToken);
            localStorage.setItem('refresh_token', refreshToken);
        }

        clearTokens() {
            this.accessToken = null;
            this.refreshToken = null;
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
        }

        async refreshAccessToken() {
            if (!this.refreshToken) {
                throw new Error('No refresh token available');
            }

            const response = await fetch('/api/v1/auth/refresh', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ refresh_token: this.refreshToken })
            });

            if (response.ok) {
                const data = await response.json();
                this.setTokens(data.access_token, this.refreshToken);
                return data.access_token;
            } else {
                this.clearTokens();
                throw new Error('Token refresh failed');
            }
        }
    }

API Usage Best Practices

1. **Use pagination**: Don't fetch all records at once
2. **Implement caching**: Cache frequently accessed data
3. **Handle errors gracefully**: Provide meaningful error messages
4. **Respect rate limits**: Implement backoff strategies
5. **Use appropriate HTTP methods**: GET for reading, POST for creating, etc.
6. **Validate input**: Validate data before sending to API
7. **Use HTTPS**: Always use secure connections

**Pagination Example**:

.. code-block:: python

    async def get_all_supplements(client, batch_size=50):
        all_supplements = []
        skip = 0
        
        while True:
            response = await client.get_supplements(skip=skip, limit=batch_size)
            supplements = response['data']
            
            if not supplements:
                break
                
            all_supplements.extend(supplements)
            skip += batch_size
            
            # Respect rate limits
            await asyncio.sleep(0.1)
        
        return all_supplements

SDK and Client Libraries
========================

Official Python SDK

.. code-block:: bash

    # Install the official Python SDK (when available)
    pip install supplement-tracker-sdk

.. code-block:: python

    from supplement_tracker import SupplementTracker

    # Initialize client
    client = SupplementTracker(
        base_url="https://api.supplementtracker.com",
        api_key="your_api_key"  # or use username/password
    )

    # Authenticate
    await client.authenticate(username="testuser", password="password")

    # Use the client
    supplements = await client.supplements.list(search="vitamin")
    intake = await client.intakes.create(
        supplement_id="123",
        dosage=1000,
        dosage_unit="mg"
    )

Community SDKs

**JavaScript/Node.js**:

.. code-block:: bash

    npm install supplement-tracker-js

**R Package**:

.. code-block:: r

    # Install from CRAN (when available)
    install.packages("supplementtracker")

    # Or from GitHub
    devtools::install_github("supplementtracker/r-client")

Next Steps
==========

Now that you understand the API basics:

1. **Set up authentication**: :doc:`authentication`
2. **Start tracking supplements**: :doc:`supplement-tracking`
3. **Explore community features**: :doc:`community-features`
4. **Check out the full API reference**: :doc:`../api/index`

For developers:

- :doc:`../developer/setup`
- :doc:`../developer/testing`
