#!/usr/bin/env python3
"""
Fix specific title underline issues identified by the test script.
"""

import re
from pathlib import Path


def fix_underlines_in_file(file_path):
    """Fix underline issues in a specific file."""
    print(f"Fixing {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed = False
    new_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        new_lines.append(line)
        
        # Check if next line is an underline
        if i + 1 < len(lines):
            next_line = lines[i + 1]
            
            if re.match(r'^[=\-~^"\'`#*+<>_]+\s*$', next_line.strip()):
                title_len = len(line.rstrip())
                underline_len = len(next_line.rstrip())
                
                # If there's a significant mismatch, fix it
                if abs(title_len - underline_len) > 1 and title_len > 0:
                    underline_char = next_line.strip()[0] if next_line.strip() else '='
                    new_underline = underline_char * title_len + '\n'
                    new_lines.append(new_underline)
                    print(f"  Fixed line {i+2}: '{line.strip()}' (was {underline_len}, now {title_len})")
                    fixed = True
                    i += 2  # Skip the original underline
                    continue
        
        i += 1
    
    if fixed:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        return True
    
    return False


def main():
    """Fix specific files with known issues."""
    files_to_fix = [
        "docs/user-guide/configuration.rst",
        "docs/developer/contributing.rst", 
        "docs/developer/setup.rst",
        "docs/developer/coding-standards.rst"
    ]
    
    fixed_count = 0
    for file_path in files_to_fix:
        path = Path(file_path)
        if path.exists():
            if fix_underlines_in_file(path):
                fixed_count += 1
        else:
            print(f"File not found: {file_path}")
    
    print(f"\nFixed underlines in {fixed_count} files.")


if __name__ == '__main__':
    main()
